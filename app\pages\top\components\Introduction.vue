<template>
  <div class="gallery-wrapper" ref="wrapperRef">
    <div
      class="image-gallery-container"
      v-for="(item, index) in gallery"
      :key="index"
    >
      <!-- 左侧 -->
      <div class="left-images">
        <div class="top-image">
          <img :src="item.leftTopImage" :alt="item.leftTopAlt" class="image" />
        </div>

        <div class="bottom-image">
          <img
            :src="item.leftBottomImage"
            :alt="item.leftBottomAlt"
            class="image"
          />
        </div>
      </div>

      <!-- logo图标 -->
      <div class="logo-container">
        <img :src="connectivImage" class="logo-tivImage" />
      </div>

      <!-- 左侧图标 -->
      <div class="left-tivImage-container">
        <img :src="connectivLeftImag" class="left-tivImage" />
      </div>

      <!--右侧 -->
      <div class="right-images">
        <div class="horizontal-scroll-container">
          <img
            :src="item.rightImage"
            :alt="item.rightAlt"
            class="scrollable-image-horizontal"
          />

          <!-- 右侧图标-->
          <div class="right-tivImage-container">
            <img :src="connectivRightImag" class="right-tivImage" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import connectiv from "~/assets/image/connectiv.png";
import connectivLeft from "~/assets/image/connectivLeft.png";
import connectivRight from "~/assets/image/connectivRight.png";

const connectivImage = connectiv;
const connectivLeftImag = connectivLeft;
const connectivRightImag = connectivRight;
const wrapperRef = ref(null);
let intervalId = null;

const props = defineProps({
  gallery: {
    type: Array,
    required: true,
  },
});

const preventScroll = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

const preventTouch = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

const slowSmoothScrollTo = (element, targetPosition, duration = 2000) => {
  const startPosition = element.scrollLeft;
  const distance = targetPosition - startPosition;
  const startTime = performance.now();

  const animateScroll = (currentTime) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    const easeProgress =
      progress < 0.5
        ? 16 * progress * progress * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 5) / 2;

    element.scrollLeft = startPosition + distance * easeProgress;

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  };

  requestAnimationFrame(animateScroll);
};

onMounted(() => {
  const wrapper = wrapperRef.value;
  if (!wrapper) return;

  wrapper.addEventListener("wheel", preventScroll, { passive: false });
  wrapper.addEventListener("mousewheel", preventScroll, { passive: false });
  wrapper.addEventListener("DOMMouseScroll", preventScroll, { passive: false });
  wrapper.addEventListener("touchstart", preventTouch, { passive: false });
  wrapper.addEventListener("touchmove", preventTouch, { passive: false });
  wrapper.addEventListener("touchend", preventTouch, { passive: false });

  setTimeout(() => {
    let currentIndex = 0;
    const total = props.gallery.length;
    const scrollStep = wrapper.children[0]?.offsetWidth || 600;

    intervalId = setInterval(() => {
      currentIndex = (currentIndex + 1) % total;
      const targetPosition = currentIndex * scrollStep;

      slowSmoothScrollTo(wrapper, targetPosition, 4000);
    }, 5000);
  }, 500);
});

onBeforeUnmount(() => {
  clearInterval(intervalId);
  const wrapper = wrapperRef.value;
  if (wrapper) {
    wrapper.removeEventListener("wheel", preventScroll);
    wrapper.removeEventListener("mousewheel", preventScroll);
    wrapper.removeEventListener("DOMMouseScroll", preventScroll);
    wrapper.removeEventListener("touchstart", preventTouch);
    wrapper.removeEventListener("touchmove", preventTouch);
    wrapper.removeEventListener("touchend", preventTouch);
  }
});
</script>

<style scoped lang="scss">
.gallery-wrapper {
  display: flex;
  align-items: center;
  overflow-x: hidden;
  -ms-overflow-style: none;
  scrollbar-width: none;
  gap: 10px;
}

.image-gallery-container {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  width: 600px;
}

.left-images {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 223px;
  flex-shrink: 0;
  position: relative;
}

.logo-container {
  position: absolute;
  top: 130px;
  left: 50%;
  transform: translateX(-50%);
}

.logo-tivImage {
  width: 145px;
  height: 145px;
}

.left-tivImage-container {
  position: absolute;
  top: 270px;
  left: 10%;
  width: 80px;
  height: 104px;
}

.left-tivImage {
  width: 100%;
  height: 100%;
}

.right-tivImage-container {
  position: absolute;
  top: 270px;
  right: 10%;
  width: 80px;
  height: 104px;
}

.right-tivImage {
  width: 100%;
  height: 100%;
}

.top-image,
.bottom-image {
  width: 223px;
  height: 141px;
  border-radius: 12px;
  overflow: hidden;
}

.top-image img,
.bottom-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.right-images {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
}

.scrollable-image-horizontal {
  width: 100%;
  height: 292px;
  object-fit: cover;
}
@media (min-width: 1280px) {
  .left-tivImage-container {
    position: absolute;
    top: 270px;
    left: 29%;
    width: 80px;
    height: 104px;
  }
  .right-tivImage-container {
    position: absolute;
    top: 270px;
    right: 34%;
    width: 80px;
    height: 104px;
  }
}
</style>
