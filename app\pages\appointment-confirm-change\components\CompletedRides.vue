<template>
  <div class="completed-rides">
    <!-- 乗車日フィルター -->
    <div class="filter-section">
      <span class="filter-label">乗車日</span>
      <v-select
        v-model="selectedPeriod"
        :items="periodOptions"
        variant="outlined"
        density="compact"
        class="period-select"
        hide-details
      >
        <template #append-inner>
          <v-icon color="primary">mdi-chevron-down</v-icon>
        </template>
      </v-select>
    </div>

    <!-- 予約リスト -->
    <div class="reservation-list">
      <div
        v-for="(reservation, index) in filteredReservations"
        :key="index"
        class="reservation-item"
      >
        <div class="reservation-content">
          <!-- ステータスと予約番号 -->
          <div class="reservation-header">
            <div
              class="status-badge"
              :class="getStatusClass(reservation.status)"
            >
              {{ reservation.status }}
            </div>
            <span class="reservation-number">
              予約番号：{{ reservation.reservationNumber }}
            </span>
          </div>

          <!-- 乗車情報 -->
          <div class="ride-info">
            <div class="ride-date">
              {{ reservation.rideDate }}
            </div>
            <div class="route-info">
              <span class="station">{{ reservation.departure }}</span>
              <v-icon size="20" color="#9CBCD4" class="arrow-icon">
                mdi-arrow-right
              </v-icon>
              <span class="station">{{ reservation.destination }}</span>
              <v-icon
                v-if="reservation.via"
                size="20"
                color="#9CBCD4"
                class="arrow-icon"
              >
                mdi-arrow-right
              </v-icon>
              <span v-if="reservation.via" class="station">
                {{ reservation.via }}
              </span>
            </div>
          </div>
        </div>

        <!-- 展開ボタン -->
        <v-btn
          icon
          variant="text"
          color="primary"
          size="small"
          @click="toggleExpand(index)"
        >
          <v-icon>
            {{
              expandedItems.includes(index)
                ? 'mdi-chevron-up'
                : 'mdi-chevron-down'
            }}
          </v-icon>
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// インターフェース定義
interface CompletedReservation {
  status: '乗車済' | 'キャンセル済'
  reservationNumber: string
  rideDate: string
  departure: string
  destination: string
  via?: string
  period: string
}

// リアクティブデータ
const expandedItems = ref<number[]>([])
const selectedPeriod = ref('過去三ヶ月')

// 期間オプション
const periodOptions = ['過去三ヶ月', '過去六ヶ月', '過去一年']

// 完了済み予約データ（サンプル）
const reservations = ref<CompletedReservation[]>([
  {
    status: '乗車済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    via: '東京',
    period: '過去三ヶ月'
  },
  {
    status: 'キャンセル済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  },
  {
    status: 'キャンセル済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  },
  {
    status: 'キャンセル済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  },
  {
    status: '乗車済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  },
  {
    status: '乗車済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  },
  {
    status: '乗車済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    period: '過去三ヶ月'
  }
])

// 計算プロパティ
const filteredReservations = computed(() => {
  return reservations.value.filter(
    (reservation) => reservation.period === selectedPeriod.value
  )
})

// メソッド
const getStatusClass = (status: string) => {
  return {
    'status-completed': status === '乗車済',
    'status-cancelled': status === 'キャンセル済'
  }
}

const toggleExpand = (index: number) => {
  const expandIndex = expandedItems.value.indexOf(index)
  if (expandIndex > -1) {
    expandedItems.value.splice(expandIndex, 1)
  } else {
    expandedItems.value.push(index)
  }
}
</script>

<style scoped>
.completed-rides {
  background-color: #ffffff;
}

.filter-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #dfdfdf;
  background-color: #ffffff;
}

.filter-label {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  line-height: 1.375;
}

.period-select {
  width: 200px;
}

.period-select :deep(.v-field) {
  border-radius: 6px;
  height: 40px;
}

.period-select :deep(.v-field__input) {
  padding: 13px 16px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.reservation-list {
  background-color: #ffffff;
}

.reservation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #dfdfdf;
  gap: 16px;
}

.reservation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reservation-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  min-width: 58px;
  height: 24px;
}

.status-completed {
  background-color: #e7f2fa;
  color: #26499d;
  border: 1px solid #26499d;
}

.status-cancelled {
  background-color: #ffdfdf;
  color: #d00000;
  border: 1px solid #d00000;
}

.reservation-number {
  font-size: 14px;
  font-weight: 400;
  color: #7d7d7d;
  line-height: 1.571;
}

.ride-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ride-date {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  line-height: 1.375;
}

.route-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.station {
  font-size: 20px;
  font-weight: 400;
  color: #000000;
  line-height: 1.2;
}

.arrow-icon {
  margin: 0 2px;
}
</style>
