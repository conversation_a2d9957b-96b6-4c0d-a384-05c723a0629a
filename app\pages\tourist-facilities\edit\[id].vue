<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { TouristSpot } from '~/types/edit'; 
import {
		useSpotStore
	} from '~/stores/edit';
const router = useRouter()
const route = useRoute()
const spotStore = useSpotStore();

definePageMeta({
    footerPlaceholderHeight: 86
});

// 观光地数据 
const touristData = ref<TouristSpot>({
    id: 1,
    spot_id: 1001, 
    name: "東京タワー",
    description: "東京タワーは、東京都港区にある通信と観光を目的としたタワーです。1958年に完成し、高さ333メートルです。東京のシンボル的存在として知られ、夜間はライトアップされ美しい景観を楽しめます。",
    address: "東京都港区芝公園4丁目2-8",
    homepage: "https://www.tokyotower.co.jp",
    images: [
        'https://picsum.photos/400/300?random=5',
        'https://picsum.photos/400/300?random=3',
        'https://picsum.photos/400/300?random=4'
    ],
    category: "観光タワー", 
    prefecture: "東武日光駅", 
    nearest_bus_stop_id: 501, 
    latitude: 35.6586,
    longitude: 139.7454, 
    opening_hours: "9:00 - 23:00", 
    holiday: "なし", 
    parking_available: false 
});


// 当前轮播索引
const currentSlide = ref<number>(0)

// 轮播定时器
let carouselTimer: NodeJS.Timeout | null = null

// 跳转到指定轮播
const goToSlide = (index: number): void => {
    currentSlide.value = index
    resetTimer()
}

// 下一张轮播
const nextSlide = (): void => {
    currentSlide.value = (currentSlide.value + 1) % touristData.value.images.length
}

// 启动自动轮播
const startAutoPlay = (): void => {
    carouselTimer = setInterval(nextSlide, 3000) // 3秒自动切换
}

// 停止自动轮播
const stopAutoPlay = (): void => {
    if (carouselTimer) {
        clearInterval(carouselTimer)
        carouselTimer = null
    }
}

// 重置定时器
const resetTimer = (): void => {
    stopAutoPlay()
    startAutoPlay()
}


function handleBack() {
    router.go(-1);
}

function contentSearch() {
    spotStore.setCurrentTouristData(touristData.value);
  router.push({
    path: '/tourist-facilities/routeSelect',
  });
}


const touristId = ref<number | null>(null)

// 组件挂载时启动自动轮播
onMounted(() => {
    startAutoPlay()

    // 获取路由参数id
    const id = route.params.id
    touristId.value = id ? Number(id) : null
    console.log('接收的ID:', touristId.value)
})

// 组件卸载时清除定时器
onUnmounted(() => {
    stopAutoPlay()
})

</script>

<template>
    <div>
        <!-- 标题栏 -->
        <div class="regionHeaderTop createPad">
            <button class="regionSelectorBackBtn" @click="handleBack">
                <svg width="16" height="16" viewBox="0 0 16 16">
                    <path d="M10 4L6 8l4 4" stroke="#26499D" stroke-width="1.5" fill="none" />
                </svg>
            </button>
            <h3 class="regionSelectorTitle">観光地・施設</h3>
        </div>

        <div class="touristAttractionCard">
            <!-- 轮播图区域 -->
            <div class="carouselContainer">
                <div class="carouselWrapper" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                    <div v-for="(image, index) in touristData.images" :key="index" class="carouselSlide"
                        :style="{ backgroundImage: `url(${image})` }">
                    </div>
                </div>
            </div>


            <!-- 轮播指示器 -->
            <div class="carouselIndicators">
                <button v-for="(image, index) in touristData.images" :key="index"
                    :class="['indicator', { active: currentSlide === index }]" @click="goToSlide(index)"
                    :style="{ backgroundImage: `url(${image})` }" :aria-label="`スライド ${index + 1} を表示`"></button>
            </div>

            <!-- 卡片内容区域 -->
            <div class="cardContent">
                <!-- 标题 -->
                <h2 class="cardTitle">{{ touristData.name }}</h2>

                <!-- 概要部分 -->
                <div class="overviewSection">
                    <h3 class="sectionTitle">概要</h3>
                    <p class="description">
                        {{ touristData.description }}
                    </p>
                </div>

                <!-- 所在地部分 -->
                <div class="locationSection">
                    <h3 class="sectionTitle">所在地</h3>
                    <p class="locationText">{{ touristData.address }}</p>
                </div>

                <!-- 主页链接部分 -->
                <div class="homepageSection">
                    <h3 class="sectionTitle">ホームページ</h3>
                    <a :href="touristData.homepage" class="homepageLink">{{ touristData.homepage }}</a>
                </div>
            </div>
        </div>

        <div class="fixedBottomContainer" @click="contentSearch">
            <div class="content">
                この目的地でバス検索
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>

.fixedBottomContainer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    height: 86px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content {
    text-align: center;
    font-size: 16px;
    color: #fff;
    background-color: #26499D;
    width: 343px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
}

.touristAttractionCard {
    background: white;
    border-radius: 4px;
    overflow: hidden;

    .carouselContainer {
        position: relative;
        width: 100%;
        height: 250px;
        overflow: hidden;
        background-color: #f5f5f5;

        .carouselWrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;

            .carouselSlide {
                flex: 0 0 100%;
                height: 100%;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }
        }
    }


    .carouselIndicators {
        display: flex;
        gap: 6px;
        margin-top: 8px;
        padding: 0 20px;

        .indicator {
            width: 51px;
            height: 38px;
            border: 2px solid transparent;
            background-size: cover;
            background-position: center;
            cursor: pointer;
            transition: all 0.3s ease;

            &.active {
                border-color: #26499D;
                opacity: 1;
            }

            &:not(.active) {
                opacity: 0.7;
            }

            &:hover {
                opacity: 1;
            }
        }
    }

    .cardContent {
        padding: 20px;
        line-height: 1.6;

        .cardTitle {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0 0 20px 0;
            padding: 0;
        }

        .overviewSection,
        .locationSection,
        .homepageSection {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .sectionTitle {
            color: #0066cc;
            font-weight: normal;
            background-color: #E7F2FA;
            width: 88px;
            height: 22px;
            text-align: center;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .description {
            font-size: 12px;
            color: #333;
            line-height: 1.5;
            margin: 0;
            text-align: justify;
        }

        .locationText {
            font-size: 13px;
            color: #333;
            margin: 0;
        }

        .homepageLink {
            font-size: 13px;
            color: #0066cc;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
</style>
