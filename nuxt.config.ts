// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  ssr: true,
  css: ['vuetify/styles', '~/assets/styles/index.scss'],
  app: {
    head: {
      link: [
        // 预加载关键资源
        {
          rel: 'preload',
          href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap',
          as: 'style'
        },
        // 然后正常加载
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap'
        }
      ]
    }
  },
  build: {
    transpile: ['vuetify']
  },
  modules: ['@nuxt/eslint', '@nuxt/test-utils', '@nuxt/image', 'vuetify-nuxt-module', '@pinia/nuxt'],
  vuetify: {
    moduleOptions: {
      // 启用SSR支持
      ssrClientHints: {
        viewportSize: true,
        prefersColorScheme: true,
        prefersReducedMotion: true
      }
    },
    vuetifyOptions: {
      ssr: {
        clientWidth: 1200,
        clientHeight: 800 
      },
      display: {
        mobileBreakpoint: 'sm'
      }
    }
  },
  vite: {
    build: { sourcemap: false },
    server: {
      fs: {
        // 允许访问到上级目录，避免 E:/... 被直接暴露
        // Windows + Vite + SSR 下，因为 Vite 把 绝对路径（E:/...）直接暴露给浏览器，但浏览器根本访问不到本地磁盘路径。
        //  Nuxt 4 dev 模式在 Vite 的 @fs 协议上有 bug（特别是 Windows 环境）。
        allow: [
          './' // 项目根目录
        ]
      },
      proxy: {
        '/api': {
          target: 'http://192.168.0.42:8080',
          changeOrigin: true,
          secure: false
        }
      }
    }
  }
})
