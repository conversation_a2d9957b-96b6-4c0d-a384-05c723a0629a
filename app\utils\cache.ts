export enum StorageType {
  LOCAL = 'local',
  SESSION = 'session',
}

class LocalCache {
  setCache(key: string, value: unknown, storageType: StorageType = StorageType.LOCAL) {
    if (storageType === StorageType.LOCAL) {
      window?.localStorage.setItem(key, JSON.stringify(value))
    } else {
      window?.sessionStorage.setItem(key, JSON.stringify(value))
    }
  }

  getCache(key: string, storageType: StorageType = StorageType.LOCAL) {
    let value: unknown
    if (storageType === StorageType.LOCAL) {
      value = window?.localStorage.getItem(key)
    } else {
      value = window?.sessionStorage.getItem(key)
    }
    try {
      return value ? JSON.parse(value as string) : value
    } catch (error) {
      console.error('JSON 解析に失敗しました:', error)
      return value
    }
  }

  deleteCache(key: string, storageType: StorageType = StorageType.LOCAL) {
    if (storageType === StorageType.LOCAL) {
      window?.localStorage.removeItem(key)
    } else {
      window?.sessionStorage.removeItem(key)
    }
  }

  clearCache(storageType?: StorageType) {
    if (storageType === StorageType.LOCAL || storageType === undefined) {
      window?.localStorage.clear()
    }

    if (storageType === StorageType.SESSION || storageType === undefined) {
      window?.sessionStorage.clear()
    }
  }
}

export default new LocalCache()
