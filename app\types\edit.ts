export interface TouristSpot {
  // 已有的属性
  id: number;
  name: string;
  description: string;
  address: string;
  homepage: string;
  images: string[];
  
  // 补充缺失的属性
  spot_id: number;
  category: string; // 例如: "観光地", "レストラン", "ショッピング" 等
  prefecture: string; // 都道府県，例如: "東京都"
  nearest_bus_stop_id: number | null; // 最近的公交站ID，可为null
  latitude: number; // 纬度
  longitude: number; // 经度
  opening_hours: string; // 营业时间
  holiday: string; // 休息日
  parking_available: boolean; // 是否有停车场
}
