<script setup lang="ts">
import { ref, nextTick, computed } from 'vue'

// 定义行程类型
interface Trip {
  id: string
  tripHeader: string
  isRouteExpanded?: boolean
  isContentVisible?: boolean
  [key: string]: any // 允许其他属性存在
}

// 定义预约单类型
interface Ticket {
  id: string
  arr: Trip[]
  [key: string]: any // 允许其他属性存在
}

// 接收父组件传递的tickets数组，添加类型定义
const props = defineProps<{
  tickets: Ticket[]
}>()

// 存储每个预约单的当前页码（key: ticketId, value: pageNum）
const ticketPages = ref<Record<string, number>>({})

// 初始化所有预约单的页码为1
props.tickets.forEach((ticket) => {
  ticketPages.value[ticket.id] = 1
})

// 当前激活的预约单ID
const activeTicketId = ref<string>(
  props.tickets.length > 0 && props.tickets[0]?.id ? props.tickets[0].id : ''
)

// 获取指定预约单的可见行程
const getVisibleTrips = (ticket: Ticket): Trip[] => {
  const currentPage = ticketPages.value[ticket.id] || 1
  const startIndex = (currentPage - 1) * 2
  let endIndex = startIndex + 2

  // 检查是否是最后一页且总行程数为单数
  if (currentPage === getTotalPages(ticket) && ticket.arr.length % 2 !== 0) {
    endIndex = startIndex + 1
  }

  return ticket.arr.slice(startIndex, endIndex)
}

// 获取指定预约单的总页数
const getTotalPages = (ticket: Ticket): number => {
  return Math.ceil(ticket.arr.length / 2)
}

// 获取预约单ID列表（用于导航）
const ticketIds = computed<string[]>(() => {
  return props.tickets.map((ticket) => ticket.id)
})

// 获取当前激活预约单的索引
const activeIndex = computed<number>(() => {
  return ticketIds.value.indexOf(activeTicketId.value)
})

// 切换指定预约单的页面（行程分页）
const switchTicketPage = async (ticket: Ticket, pageNum: number) => {
  ticketPages.value[ticket.id] = pageNum

  await nextTick()
  const scrollContainer = document.querySelector(
    `.ticketScrollContainer-${ticket.id}`
  )
  const targetCard = scrollContainer?.children[0]
  if (targetCard) {
    scrollContainer.scrollTo({
      left: (targetCard as HTMLElement).offsetLeft - 16,
      behavior: 'smooth'
    })
  }
}

// 切换到指定ID的预约单
const switchToTicket = (ticketId: string) => {
  activeTicketId.value = ticketId

  // 滚动到当前激活的预约单
  nextTick(() => {
    const index = ticketIds.value.indexOf(ticketId)
    const activeElement = document.querySelector(
      `.ticketBox:nth-child(${index + 1})`
    )
    if (activeElement) {
      activeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      })
    }
  })
}

// 下一个预约单
const nextTicket = () => {
  const currentIndex = activeIndex.value
  const nextIndex =
    currentIndex === ticketIds.value.length - 1 ? 0 : currentIndex + 1
  switchToTicket(ticketIds.value[nextIndex]!)
}

// 上一个预约单
const prevTicket = () => {
  const currentIndex = activeIndex.value
  const prevIndex =
    currentIndex === 0 ? ticketIds.value.length - 1 : currentIndex - 1
  switchToTicket(ticketIds.value[prevIndex]!)
}

// 下一页逻辑（针对指定预约单的行程）
const nextTicketPage = (ticket: Ticket) => {
  const currentPage = ticketPages.value[ticket.id] || 1
  if (currentPage < getTotalPages(ticket)) {
    switchTicketPage(ticket, currentPage + 1)
  } else {
    // 循环到开始
    switchTicketPage(ticket, 1)
  }
}

// 路线展开/收起逻辑
const toggleRouteExpand = (trip: Trip) => {
  trip.isRouteExpanded = !trip.isRouteExpanded
  trip.isContentVisible = !trip.isContentVisible
}

// 显示乘车券逻辑
const showTicket = (trip: Trip, reservationId: string) => {
  console.log(`显示【${trip.tripHeader}】乘车券，原始预约号：${reservationId}`)
}

// 切换到下一个行程（针对指定预约单）
const showNextTrip = (ticket: Ticket) => {
  nextTicketPage(ticket)
}

// 预约确认/变更逻辑
const confirmOrChangeReservation = (trip: Trip, reservationId: string) => {
  // 切换激活的预约单ID
  activeTicketId.value = reservationId
  console.log(
    `处理【${trip.tripHeader}】预约变更，原始预约号：${reservationId}`
  )
}
</script>

<template>
  <div>
    <h2 class="sectionHeading">直近の乗車予定</h2>
    <div class="headingUnderline"></div>
    <div class="ticketMainContainer">
      <div class="hideScrollbar">
        <div
          class="ticketBox"
          v-for="(ticket, index) in tickets"
          :key="ticket.id"
          :class="{ 'active-ticket': activeTicketId === ticket.id }"
          @click="switchToTicket(ticket.id)"
        >
          <div>
            <!-- 显示当前预约单可见行程的路线 -->
            <div class="routeDisplay" v-if="getVisibleTrips(ticket).length > 0">
              <span>{{ getVisibleTrips(ticket)[0]?.start }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.end }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.next }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.final }}</span>
            </div>
            <div class="reservationNumberDisplay">
              予約番号：{{ ticket.id }}
            </div>
          </div>

          <div
            :class="`ticketScrollContainer ticketScrollContainer-${ticket.id}`"
          >
            <template
              v-for="(trip, idx) in getVisibleTrips(ticket)"
              :key="trip.id"
            >
              <div
                class="ticketCardWrapper"
                :class="{
                  'single-card': getVisibleTrips(ticket).length === 1,
                  'first-card':
                    getVisibleTrips(ticket).length === 2 && idx === 0,
                  'second-card':
                    getVisibleTrips(ticket).length === 2 && idx === 1
                }"
              >
                <div class="tripItemWrapper">
                  <div v-if="trip.isContentVisible">
                    <div class="tripInfoItem">
                      <div class="tripHeaderTitle">{{ trip.tripHeader }}</div>
                      <div class="tripDateInfo">
                        <span>{{ trip.startDate }}</span>
                        <span>{{ trip.endDate }}</span>
                      </div>
                      <div class="tripTimeInfo">
                        <span>{{ trip.startTime }}</span>
                        <span class="timeSeparatorLine">-</span>
                        <span>{{ trip.endTime }}</span>
                      </div>
                      <div class="tripLocationInfo">
                        <span>{{ trip.startLocation }}</span>
                        <span>{{ trip.endLocation }}</span>
                      </div>
                      <button
                        class="showTicketButton"
                        @click="showTicket(trip, ticket.id)"
                      >
                        乗車券を表示
                        <img
                          src="~/assets/image/ticket.png"
                          class="ticketIconImg"
                          alt="乗車券アイコン"
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 只在显示两张卡片时显示中间的三角形图标 -->
              <img
                v-if="getVisibleTrips(ticket).length === 2 && idx === 0"
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="区切りアイコン"
              />
            </template>
          </div>

          <!-- 下一个行程按钮和预约操作区 -->
          <div class="actionsContainer">
            <div
              class="nextTripButton"
              @click="showNextTrip(ticket)"
              v-if="ticket.arr.length > 1"
            >
              次の便
              <img
                src="~/assets/image/Icon.png"
                class="nextTripIcon"
                alt="次へアイコン"
              />
            </div>
            <div
              class="reservationActionsArea"
              @click="
                confirmOrChangeReservation(
                  getVisibleTrips(ticket)[0]!,
                  ticket.id
                )
              "
            >
              <span>予約確認・変更</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约单分页按钮（全局预约单切换） -->
    <div class="page-nav" v-if="ticketIds.length > 1">
      <img
        src="~/assets/image/Icon.png"
        class="prevGlobalPageIcon"
        @click="prevTicket"
        alt="前へアイコン"
        v-if="activeIndex > 0"
      />

      <button
        v-for="(ticketId, index) in ticketIds"
        :key="ticketId"
        class="page-btn"
        :class="{ active: activeTicketId === ticketId }"
        @click="switchToTicket(ticketId)"
      >
        {{ index + 1 }}
      </button>

      <img
        src="~/assets/image/Icon.png"
        class="nextGlobalPageIcon"
        @click="nextTicket"
        alt="次へアイコン"
        v-if="activeIndex < ticketIds.length - 1"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.hideScrollbar {
  display: flex;
  gap: 16px;
  min-width: 74rem;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hideScrollbar::-webkit-scrollbar {
  display: none;
}

.reservationMainCard {
  background-color: white;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 20px;
  border: 1px solid #f3f4f6;
}

.cardHeader {
  background-color: #eff6ff;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.headerTitle {
  color: #26499d;
  font-weight: 500;
  font-size: 18px;
  text-align: center;
  margin: 0;
}

.cardContent {
  padding: 32px 24px;
  text-align: center;
}

.contentMessage {
  color: #4b5563;
  margin: 0;
}

.triangleIcon {
  width: 13px;
  height: 8px;
  margin: 0 8px;
  align-self: center;
}

.ticketMainContainer {
  padding: 0 20px 20px 20px;
  text-align: right;
  margin-left: auto;
  margin-right: 0;
  overflow: hidden;
}

.sectionHeading {
  text-align: center !important;
  color: #26499d;
  font-size: 18px;
  font-weight: 600;
  margin: 0 auto 8px;
  display: block;
  width: 100%;
  padding-top: 60px;
}

.headingUnderline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

.ticketBox {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  min-width: 54%;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &.active-ticket {
    box-shadow: 0 4px 12px rgba(38, 73, 157, 0.2);
    transform: translateY(-2px);
  }

  .ticketScrollContainer {
    display: flex;
    padding: 20px 0;
    overflow-x: auto;
    scrollbar-width: thin;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    scroll-behavior: smooth;
    justify-content: center;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #aaa;
    }
  }
}

.ticketCardWrapper {
  background-color: #fff;
  border-radius: 8px;
  width: 320px;
  flex: 0 0 auto;
  height: fit-content;

  &.single-card {
    margin: 0 auto;
  }

  &.first-card {
    margin-right: 20px;
  }

  &.second-card {
    margin-left: 20px;
  }
}

.routeDisplay {
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #e7f2fa;
  height: 50px;
  border-radius: 8px 8px 0 0;
}

/* 预约号显示 */
.reservationNumberDisplay {
  font-size: 14px;
  text-align: center;
  color: #26499d;
  margin: 16px 0 0 0;
}

/* 行程信息卡片 */
.tripInfoItem {
  background-color: #f3faff;
  border-radius: 6px;
  border: 1px solid #acd1ed;
  padding-bottom: 16px;
  margin: 15px;
}

.tripHeaderTitle {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #26499d;
  background-color: #acd1ed;
  padding: 4px 0;
  border-radius: 4px 4px 0 0;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tripDateInfo {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #555;
  padding: 18px 25px 6px 25px;
}

.tripTimeInfo {
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  font-weight: bold;
  color: #26499d;
  padding: 0 25px 6px 25px;

  .timeSeparatorLine {
    width: 14px;
    height: 5px;
    color: #ccc;
  }
}

.tripLocationInfo {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #555;
  padding: 0 25px 16px 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 显示乘车券按钮 */
.showTicketButton {
  background-color: #2d5199;
  color: #fff;
  border-radius: 40px;
  padding: 16px 0px;
  font-size: 14px;
  transition: background-color 0.3s ease;
  width: 230px;
  height: 50px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;

  .ticketIconImg {
    width: 22px;
    height: 22px;
    margin-left: 10px;
  }
}

.showTicketButton:hover {
  background-color: #22407d;
}

/* 行程分页按钮样式 */
.tripPagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.tripPageButton {
  width: 26px;
  height: 26px;
  color: #26499d;
  margin: 0 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
}

.tripPageButton.active {
  background-color: #e7f2fa;
  color: #26499d;
  border-radius: 50%;
}

/* 下一个行程按钮 */
.nextTripButton {
  font-size: 14px;
  color: #26499d;
  text-align: right;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transition: color 0.3s ease;
  margin-right: 20px;
  cursor: pointer;
}

.nextTripIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.prevPageIcon,
.nextPageIcon {
  width: 20px;
  height: 20px;
  margin: 0 5px;
  cursor: pointer;
}

.prevPageIcon {
  transform: rotate(-90deg);
}

.nextPageIcon {
  transform: rotate(90deg);
}

.actionsContainer {
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.reservationActionsArea {
  font-size: 14px;
  color: #26499d;
  text-align: center;
  transition: background-color 0.3s ease;
  border-top: 1px solid #9cbcd4;
  padding: 16px 20px;
  cursor: pointer;

  span {
    border-bottom: 1px solid #26499d;
  }
}

.page-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  padding: 10px 0;
}

.page-btn {
  width: 30px;
  height: 30px;
  color: #26499d;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.page-btn.active {
  background-color: #e7f2fa;
  color: #26499d;
  border-color: #2d5199;
  border-radius: 50%;
}

.prevGlobalPageIcon,
.nextGlobalPageIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.prevGlobalPageIcon {
  transform: rotate(-90deg);
  margin-right: 8px;
}

.nextGlobalPageIcon {
  transform: rotate(90deg);
  margin-left: 8px;
}
</style>
