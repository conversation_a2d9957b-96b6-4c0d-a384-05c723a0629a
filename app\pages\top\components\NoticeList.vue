<template>
  <section class="notice-section">
    <div class="notice-container">
      <!-- 标题 -->
      <h2 class="notice-title">お知らせ</h2>
      <div class="title-underline"></div>

      <div v-if="!notices.length && !pending" class="empty-container">
        <v-icon icon="mdi-information-outline" size="40" color="grey"></v-icon>
        <p class="empty-text">お知らせがありません</p>
      </div>

      <ul v-else class="notice-list">
        <li v-for="notice in notices" :key="notice.id" class="notice-item">
          <!-- 手机端：头部（日期 + 徽标） -->
          <div class="row-head mobile-only">
            <time class="date" :datetime="notice.publishStartDate">
              {{ formatDate(notice.publishStartDate) }}
            </time>
            <span class="badge" :class="badgeClass(notice.categoryName)">
              {{ notice.categoryName }}
            </span>
          </div>

          <div class="row-body mobile-only">
            <a
                class="title"
                href="#"
                @click.prevent="navigateToDetail(notice.id)"
                :aria-label="`${notice.title}の詳細を見る`"
            >
              {{ notice.title }}
            </a>
          </div>

          <div class="row-desktop desktop-only">
            <div class="col-left">
              <time class="date" :datetime="notice.publishStartDate">
                {{ formatDate(notice.publishStartDate) }}
              </time>
              <span class="badge" :class="badgeClass(notice.categoryName)">
                {{ notice.categoryName }}
              </span>
            </div>
            <div class="col-right">
              <a
                  class="title"
                  href="#"
                  @click.prevent="navigateToDetail(notice.id)"
                  :aria-label="`${notice.title}の詳細を見る`"
              >
                {{ notice.title }}
              </a>
              <p class="excerpt" v-if="notice.excerpt">
                {{ notice.excerpt }}
              </p>
            </div>
          </div>

          <hr class="divider" />
        </li>
      </ul>
    </div>

    <div class="more-wrap" v-if="notices.length > 0">
        <v-btn @click="navigateToNoticeList" variant="outlined" :disabled="pending">
          もっと見る
        </v-btn>
    </div>

    <!-- 错误提示Snackbar -->
    <v-snackbar v-model="showError" color="error" timeout="4000" variant="tonal">
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">閉じる</v-btn>
      </template>
    </v-snackbar>
  </section>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

// 定义API返回的数据类型（小驼峰）
interface NoticeApiResponse {
  id: number
  title: string
  categoryId: number
  categoryName: string
  publishStartDate: string // ISO string
  publishEndDate: string // ISO string
  isPublished: boolean
  createdDateTime: string
  createdById: string
  updatedDateTime: string
  updatedById: string
  excerpt?: string // 可选的摘要字段
}

interface ApiResponse {
  data: NoticeApiResponse[]
  total?: number
  page?: number
  pageSize?: number
}

const router = useRouter()

// 响应式状态
const notices = ref<NoticeApiResponse[]>([])
const pending = ref(false)
const showError = ref(false)
const errorMessage = ref('')

// Props（可选，用于外部传入数据）
const props = defineProps<{
  maxItems?: number // 最大显示条数
  apiEndpoint?: string // API端点
}>()

// 获取通知数据
const fetchNotices = async () => {

  pending.value = true
  notices.value = getMockData()
  try {
    // const endpoint = props.apiEndpoint || '/api/notices'
    // const params = new URLSearchParams()
    //
    // if (props.maxItems) {
    //   params.append('limit', props.maxItems.toString())
    // }
    //
    // // 只获取已发布的通知
    // params.append('published', 'true')
    // params.append('sort', 'publishStartDate:desc') // 按发布日期降序
    //
    // const response = await $fetch<ApiResponse>(`${endpoint}?${params.toString()}`)
    //
    // if (response?.data) {
    //   notices.value = response.data
    // } else {
    //   throw new Error('Invalid response format')
    // }
  } catch (err) {
    console.error('Failed to fetch notices:', err)

    // 显示错误提示
    errorMessage.value = 'お知らせの取得に失敗しました'
    showError.value = true

  } finally {
    pending.value = false
  }
}

// 关闭错误提示
const closeSnackbar = () => {
  showError.value = false
}

// 导航到详情页面
const navigateToDetail = (noticeId: number) => {
  router.push(`/notices/${noticeId}`)
}

// 导航到通知列表页面
const navigateToNoticeList = () => {
  router.push('/notices')
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const y = date.getFullYear()
  const m = `${date.getMonth() + 1}`.padStart(2, '0')
  const day = `${date.getDate()}`.padStart(2, '0')
  return `${y}.${m}.${day}`
}

// 根据分类名称返回徽标样式
const badgeClass = (categoryName: string) => {
  if (categoryName === 'ニュース') {
    return 'is-news'
  }
  // 其他所有分类都使用默认灰色边框样式
  return 'is-default'
}

// 模拟数据（开发时使用）
const getMockData = (): NoticeApiResponse[] => [
  {
    id: 1,
    title: 'システムメンテナンスのお知らせ',
    categoryId: 1,
    categoryName: 'メンテナンス',
    publishStartDate: '2025-01-15T00:00:00Z',
    publishEndDate: '2025-02-15T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-10T10:00:00Z',
    createdById: 'admin',
    updatedDateTime: '2025-01-10T10:00:00Z',
    updatedById: 'admin',
    excerpt: 'システムメンテナンスにより、一時的にサービスがご利用いただけません。'
  },
  {
    id: 2,
    title: '新機能リリースのお知らせ',
    categoryId: 2,
    categoryName: 'ニュース',
    publishStartDate: '2025-01-10T00:00:00Z',
    publishEndDate: '2025-02-10T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-05T15:30:00Z',
    createdById: 'admin',
    updatedDateTime: '2025-01-05T15:30:00Z',
    updatedById: 'admin',
    excerpt: '新しい機能が追加されました。ぜひお試しください。'
  },
  {
    id: 3,
    title: '大雪による運行への影響について',
    categoryId: 3,
    categoryName: '運行状況',
    publishStartDate: '2025-01-08T00:00:00Z',
    publishEndDate: '2025-01-20T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-08T08:00:00Z',
    createdById: 'operator',
    updatedDateTime: '2025-01-08T08:00:00Z',
    updatedById: 'operator',
    excerpt: '大雪の影響により、一部路線で遅延や運休が発生しております。'
  }
]

// 组件挂载时获取数据
onMounted(() => {
  fetchNotices()
})

</script>

<style scoped>
/* ===== Design Tokens ===== */
:root,
:host {
  --bg-page: #f0f0f0;
  --bg-card: #ffffff;
  --ink-strong: #0c2e8a;
  --ink: #222;
  --ink-weak: #666;
  --border: #e9e9ee;
  --badge-new-bg: #e86f5a;
  --badge-maint-bg: #ffecd9;
  --badge-maint-fg: #ff7f32;
  --badge-status-bg: #ffe6e6;
  --badge-status-fg: #d23c3c;
  --badge-event-bg: #e3f2fd;
  --badge-event-fg: #1976d2;
  --focus: #2b6cff;
}

/* ===== Section & Container ===== */
.notice-section {
  background: var(--bg-page);
}

.notice-container {
  background: var(--bg-card);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 17px;
  min-height: 200px;
}

/* ===== Empty State ===== */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-text {
  margin: 16px 0 8px;
  color: var(--ink-weak);
  font-size: 14px;
}

/* ===== Title ===== */
.notice-title {
  text-align: center;
  color: #26499d;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  padding-top: 20px;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

/* ===== List & Items ===== */
.notice-list {
  margin: 12px 0 8px;
  padding: 0;
  list-style: none;
}

.notice-item {
  padding: 8px 0 0;
}

.row-head {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date {
  font-size: 12px;
  color: #333;
  letter-spacing: 0.02em;
  margin-right: 16px;
}

.badge {
  display: inline-block;
  font-size: 11px;
  font-weight: 700;
  line-height: 1;
  padding: 6px 10px 5px;
  border: 1px solid transparent;
  border-radius: 4px;
  transform: translateY(-1px);
}

.badge.is-news {
  background: #ed785f;
  color: #fff;
  border: none;
}

.badge.is-default {
  background: #fff;
  color: #ed785f;
  border: 1px solid #ed785f;
}

.row-body .title,
.col-right .title {
  display: inline-block;
  margin-top: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--ink);
  text-decoration: none;
  transition: color 0.2s ease;
}

.row-body .title:hover,
.col-right .title:hover {
  color: #26499d;
  text-decoration: underline;
}

.divider {
  margin: 8px 0 0;
  border: 0;
  border-top: 1px solid #dfdfdf;
}

/* ===== PC 布局 ===== */
.desktop-only {
  display: none;
}

.mobile-only {
  display: block;
}

@media (min-width: 992px) {
  .notice-container {
    max-width: 58%;
    margin: 0 auto;
  }

  .notice-title {
    font-size: 22px;
  }

  .notice-list {
    margin-top: 24px;
  }

  .desktop-only {
    display: block;
  }

  .mobile-only {
    display: none;
  }

  .row-desktop {
    display: grid;
    grid-template-columns: 170px 1fr;
    column-gap: 28px;
    align-items: start;
  }

  .col-left .date {
    display: block;
    font-size: 12px;
    margin-bottom: 10px;
  }

  .col-left .badge {
    display: inline-block;
  }

  .col-right .title {
    margin-top: 0;
    margin-bottom: 6px;
    font-size: 16px;
    font-weight: 700;
  }

  .excerpt {
    margin: 0 0 6px;
    font-size: 13px;
    line-height: 1.7;
    color: var(--ink-weak);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }

  .divider {
    margin-top: 14px;
  }
}

/* ===== More Button ===== */
.more-wrap {
  display: flex;
  justify-content: center;
  margin: 48px 0;
}

.more-btn button {
  padding: 12px 24px;
  border-radius: 24px;
  border: 1px solid #26499d;
  background: #fff;
  color: #26499d;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.more-btn button:hover:not(:disabled) {
  background: #26499d;
  color: #fff;
}

.more-btn button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>