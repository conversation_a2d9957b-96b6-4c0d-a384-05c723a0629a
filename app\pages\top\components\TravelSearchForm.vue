<script setup lang="ts">
import { ref, computed, provide } from 'vue'
import { useDisplay } from 'vuetify'
import { useDateTimeHandler } from '../../../composables/top/useDateTimeHandler'

// 类型定义
interface OneWayFormData {
  departure: string
  destination: string
  date: string
  time: string
  direction: 'departure' | 'arrival'
  waypoints: Array<{ id: number; location: string }>
}

interface RoundTripFormData {
  departure: string
  destination: string
  outbound: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
  }
  return: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
  }
  waypoints: Array<{ id: number; location: string }>
}

interface ItineraryItem {
  departure: string
  destination: string
  date: string
  time: string
  tripType: 'departure' | 'arrival'
}

interface MyRouteFormData {
  itineraries: ItineraryItem[]
}

interface FormData {
  oneway: OneWayFormData
  roundtrip: RoundTripFormData
  myroute: MyRouteFormData
}

interface SearchData {
  type: string

  [key: string]: any
}

// 组件导入
import OneWayForm from './OneWayForm.vue'
import RoundTripForm from './RoundTripForm.vue'
import MyRouteForm from './MyRouteForm.vue'
import DatePicker from '~/components/DatePicker.vue'
import TimePicker from '~/components/TimePicker.vue'

// 图片资源
import ForwardArrowIcon from '~/assets/image/ForwardArrowwithNodes.svg'

// Composables
const { mobile } = useDisplay()
const {
  datePickerOpen,
  timePickerOpen,
  currentDateType,
  currentTimeType,
  currentIndex,
  closeDatePicker,
  closeTimePicker
} = useDateTimeHandler()

// Props 定义
interface Props {
  initialDeparture?: string
  initialDestination?: string
  defaultTab?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialDeparture: '',
  initialDestination: '',
  defaultTab: '片道'
})

// Emits 定义
const emit = defineEmits<{
  search: [data: SearchData]
  locationSwap: [data: any]
  waypointToggle: []
  dateSelect: [date: string]
  tabClick: [tab: string]
}>()

// 活跃标签
const activeTab = ref<string>(props.defaultTab)

// 统一的表单数据管理
const formData = ref<FormData>({
  oneway: {
    departure: props.initialDeparture,
    destination: props.initialDestination,
    date: '',
    time: '',
    direction: 'departure',
    waypoints: []
  },
  roundtrip: {
    departure: props.initialDeparture,
    destination: props.initialDestination,
    outbound: {
      date: '',
      time: '',
      direction: 'departure'
    },
    return: {
      date: '',
      time: '',
      direction: 'departure'
    },
    waypoints: []
  },
  myroute: {
    itineraries: [
      {
        departure: '',
        destination: '',
        date: '',
        time: '',
        tripType: 'departure'
      }
    ]
  }
})

// Tab图标获取
const getTabIcon = (tabName: string): string | undefined => {
  if (mobile.value) return undefined

  const iconMap: Record<string, string> = {
    片道: 'mdi-arrow-right-thin',
    往復: 'mdi-swap-horizontal',
    Myルート作成: 'mdi-map-marker-path'
  }
  return iconMap[tabName]
}

// 当前选中的日期和时间（用于传递给选择器组件）
const currentSelectedDate = computed((): string => {
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    return formData.value.myroute.itineraries[currentIndex.value]?.date || ''
  }

  switch (currentDateType.value) {
    case 'oneway':
      return formData.value.oneway.date
    case 'outbound':
      return formData.value.roundtrip.outbound.date
    case 'return':
      return formData.value.roundtrip.return.date
    default:
      return ''
  }
})

const currentSelectedTime = computed((): string => {
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    return formData.value.myroute.itineraries[currentIndex.value]?.time || ''
  }

  switch (currentTimeType.value) {
    case 'oneway':
      return formData.value.oneway.time
    case 'outbound':
      return formData.value.roundtrip.outbound.time
    case 'return':
      return formData.value.roundtrip.return.time
    default:
      return ''
  }
})

// Tab切换处理
const handleTabClick = (value: string): void => {
  if (value !== activeTab.value) {
    activeTab.value = value
    emit('tabClick', value)
  }
}

// 日期选择处理
const handleDateSelected = (dateString: string): void => {
  if (!dateString) return

  // 根据当前类型和索引更新对应的数据
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    // MyRoute 的日期选择
    if (formData.value.myroute.itineraries[currentIndex.value]) {
      formData.value.myroute.itineraries[currentIndex.value].date = dateString
    }
  } else {
    // 其他 tab 的日期选择
    switch (currentDateType.value) {
      case 'oneway':
        formData.value.oneway.date = dateString
        break
      case 'outbound':
        formData.value.roundtrip.outbound.date = dateString
        // 自动调整回程日期
        if (formData.value.roundtrip.return.date &&
            new Date(formData.value.roundtrip.return.date) <= new Date(dateString)) {
          const nextDay = new Date(dateString)
          nextDay.setDate(nextDay.getDate() + 1)
          formData.value.roundtrip.return.date = nextDay.toISOString().split('T')[0]
        }
        break
      case 'return':
        formData.value.roundtrip.return.date = dateString
        break
    }
  }

  emit('dateSelect', dateString)
  closeDatePicker()
}

// 时间选择处理
const handleTimeSelected = (timeString: string): void => {
  if (!timeString) return

  // 根据当前类型和索引更新对应的数据
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    // MyRoute 的时间选择
    if (formData.value.myroute.itineraries[currentIndex.value]) {
      formData.value.myroute.itineraries[currentIndex.value].time = timeString
    }
  } else {
    // 其他 tab 的时间选择
    switch (currentTimeType.value) {
      case 'oneway':
        formData.value.oneway.time = timeString
        break
      case 'outbound':
        formData.value.roundtrip.outbound.time = timeString
        break
      case 'return':
        formData.value.roundtrip.return.time = timeString
        break
    }
  }

  closeTimePicker()
}

// 搜索处理
const handleSearch = (searchData: SearchData): void => {
  console.log('搜索数据:', {
    type: activeTab.value,
    data: searchData
  })

  emit('search', {
    type: activeTab.value,
    ...searchData
  })
}

// 位置交换处理
const handleLocationSwap = (swapData: any): void => {
  emit('locationSwap', swapData)
}

// 提供给子组件使用的日期时间处理方法
provide('dateTimeHandler', {
  datePickerOpen,
  timePickerOpen,
  currentDateType,
  currentTimeType,
  currentIndex,
  openDatePicker: (type: string, index: number = -1) => {
    currentDateType.value = type
    currentIndex.value = index
    datePickerOpen.value = true
  },
  openTimePicker: (type: string, index: number = -1) => {
    currentTimeType.value = type
    currentIndex.value = index
    timePickerOpen.value = true
  }
})
</script>

<template>
  <div class="travel-search-wrapper">
    <v-card class="travel-search-card" elevation="8" rounded="xl">
      <!-- Tab按钮区域 -->
      <div class="flex margin">
        <div class="tabs-wrapper">
          <v-btn
              :color="activeTab === '片道' ? '#26499D' : '#fff'"
              :class="['tab-button', { 'tab-active': activeTab === '片道' }]"
              :prepend-icon="getTabIcon('片道')"
              @click="handleTabClick('片道')"
          >
            片道
          </v-btn>

          <v-btn
              :color="activeTab === '往復' ? '#26499D' : '#fff'"
              :class="['tab-button', { 'tab-active': activeTab === '往復' }]"
              :prepend-icon="getTabIcon('往復')"
              @click="handleTabClick('往復')"
          >
            往復
          </v-btn>
        </div>

        <v-btn
            :color="activeTab === 'Myルート作成' ? '#26499D' : '#fff'"
            :class="[
            'tab-but tab-button',
            { 'tab-active': activeTab === 'Myルート作成' }
          ]"
            @click="handleTabClick('Myルート作成')"
        >
          <img
              v-if="!mobile"
              :src="ForwardArrowIcon"
              alt="tab-icon"
              class="tab-icon"
          />
          Myルート作成
        </v-btn>
      </div>

      <!-- 表单内容区域 -->
      <v-card-text class="form-container">
        <!-- 片道表单 -->
        <OneWayForm
            v-if="activeTab === '片道'"
            v-model="formData.oneway"
            @search="handleSearch"
            @location-swap="handleLocationSwap"
        />

        <!-- 往復表单 -->
        <RoundTripForm
            v-if="activeTab === '往復'"
            v-model="formData.roundtrip"
            @search="handleSearch"
            @location-swap="handleLocationSwap"
        />

        <!-- Myルート作成表单 -->
        <MyRouteForm
            v-if="activeTab === 'Myルート作成'"
            v-model="formData.myroute"
            @search="handleSearch"
        />
      </v-card-text>

      <!-- 全局日期选择器 -->
      <v-overlay v-model="datePickerOpen" class="align-center justify-center">
        <DatePicker
            :selectedDateValue="currentSelectedDate"
            @date-selected="handleDateSelected"
            @close="closeDatePicker"
        />
      </v-overlay>

      <!-- 全局时间选择器 -->
      <v-overlay v-model="timePickerOpen" class="align-center justify-center">
        <TimePicker
            :selectedTimeValue="currentSelectedTime"
            @time-selected="handleTimeSelected"
            @close="closeTimePicker"
        />
      </v-overlay>
    </v-card>
  </div>
</template>

<style scoped>
.v-dialog > .v-overlay__content > .v-card > .v-card-text,
.v-dialog > .v-overlay__content > form > .v-card > .v-card-text {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent !important;
}

.tabs-wrapper {
  display: flex;
  gap: 8px;
  background-color: #d9d9d9;
  padding: 10px;
  border-radius: 40px;
  margin: 6px 8px 6px 0;
}

button.tab-button.tab-active {
  color: white !important;
  border: 1px solid #000;
}

.tab-button {
  min-width: 89px;
  height: 35px;
  font-size: 12px;
  border: 1px solid #26499d;
  color: #26499d !important;
}

.tab-but {
  padding: 0 8px;
  font-size: 12px;
  border: 1px solid #26499d;
  width: 113px;
  color: #26499d !important;
}

.travel-search-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: #fff7ec;
}

.travel-search-card {
  width: 100%;
  max-width: 380px;
  background: white !important;
  overflow: hidden;
}

/* 表单容器 */
.form-container {
  padding: 24px 20px 20px !important;
}

.tab-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* PC端样式 - 使用媒体查询 */
@media (min-width: 960px) {
  .tab-button {
    font-size: 16px;
    height: 40px;
    width: auto;
    padding: 0 16px;
    min-width: 200px;
    font-weight: 600;
  }

  .tab-button :deep(.v-btn__prepend) {
    font-size: 20px;
  }

  .tab-but {
    font-size: 16px;
    height: 40px;
    width: auto;
    padding: 0 16px;
  }

  .travel-search-card {
    max-width: 60%;
  }

  .tabs-wrapper {
    gap: 20px;
    margin: 6px 20px 6px 0;
  }

  .tab-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }
}
</style>

