<script setup lang="ts">
declare global {
  interface Window {
    google: any
    initMap: () => void
  }
}

definePageMeta({
  hideFooter: true
})

// 导入组件
import OneWayRoundTripRouteSelect from '~/components/OneWayRoundTripRouteSelect.vue'

// 在 MAP_CONFIG 后面添加
const USE_MOCK_MAP = !process.env.GOOGLE_MAPS_API_KEY || process.env.GOOGLE_MAPS_API_KEY === ''

import { computed, onMounted, ref, watch, nextTick } from 'vue'
import { useDisplay } from 'vuetify'

// 配置常量
const MAP_CONFIG = {
  DEFAULT_ZOOM: 7,
  DEFAULT_CENTER: { lat: 35.6762, lng: 139.6503 },
  ZOOM_LEVELS: {
    PREFECTURE: { min: 5, max: 7 },
    AREA: { min: 8, max: 13 },
    BUS_STOP: { min: 14, max: 18 }
  },
  MARKER_COLORS: {
    DEFAULT: '#26499d',
    DEPARTURE: '#4caf50',
    DESTINATION: '#f44336',
    WAYPOINT: '#ff9800'
  }
}

// 类型定义
interface BusStop {
  busStopId: number
  busStopName: string
  busStopShortName: string
  hasToilet: boolean
  hasWifi: boolean
  mapInfo: string
  labels: string[]
  coordinates?: { lat: number; lng: number }
}

interface Area {
  areaId: number
  areaName: string
  displayOrder: number
  mapInfo: string
  busStops: BusStop[]
  coordinates?: { lat: number; lng: number }
}

interface Prefecture {
  prefectureId: number
  prefectureName: string
  displayOrder: number
  mapInfo: string
  areas: Area[]
  coordinates?: { lat: number; lng: number }
}

interface Region {
  regionId: number
  regionName: string
  displayOrder: number
  prefectures: Prefecture[]
}

interface MapMarker {
  id: string
  name: string
  type: 'prefecture' | 'area' | 'busStop'
  coordinates: { lat: number; lng: number }
  data: Prefecture | Area | BusStop
  isSelected?: boolean
  role?: 'departure' | 'destination' | 'waypoint'
}

interface SelectedLocation {
  name: string
  id: string
  type: 'prefecture' | 'area' | 'busStop'
  coordinates: { lat: number; lng: number }
  busStopInfo?: string
}

interface TripInfo {
  date: string
  time: string
  direction: 'departure' | 'arrival'
}

interface SearchFormData {
  tripType: 'oneway' | 'roundtrip'
  departure: string
  departureId: string
  departureType: '' | 'prefecture' | 'area' | 'busStop'
  destination: string
  destinationId: string
  destinationType: '' | 'prefecture' | 'area' | 'busStop'
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: '' | 'prefecture' | 'area' | 'busStop'
  }>
  date?: string
  time?: string
  direction?: 'departure' | 'arrival'
  outbound?: TripInfo
  return?: TripInfo
}

// Composables
const { mobile } = useDisplay()

// 响应式数据
const mapContainer = ref<HTMLElement>()
const googleMap = ref<any>(null)
const isMapLoading = ref(true)
const regions = ref<Region[]>([])
const currentZoom = ref(MAP_CONFIG.DEFAULT_ZOOM)
const mapMarkers = ref<MapMarker[]>([])
const selectedMarker = ref<MapMarker | null>(null)
const showMarkerModal = ref(false)
const isPanelExpanded = ref(false)

// 统一的位置状态管理
const selectedLocations = ref({
  departure: null as SelectedLocation | null,
  destination: null as SelectedLocation | null,
  waypoints: [] as SelectedLocation[]
})

// 统一的表单数据
const formData = ref<SearchFormData>({
  tripType: 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure'
})

// 计算属性
const visibleMarkers = computed<MapMarker[]>(() => {
  const zoom = currentZoom.value
  if (zoom >= MAP_CONFIG.ZOOM_LEVELS.PREFECTURE.min && zoom <= MAP_CONFIG.ZOOM_LEVELS.PREFECTURE.max) {
    return mapMarkers.value.filter((marker) => marker.type === 'prefecture')
  } else if (zoom >= MAP_CONFIG.ZOOM_LEVELS.AREA.min && zoom <= MAP_CONFIG.ZOOM_LEVELS.AREA.max) {
    return mapMarkers.value.filter((marker) => marker.type === 'area')
  } else if (zoom >= MAP_CONFIG.ZOOM_LEVELS.BUS_STOP.min && zoom <= MAP_CONFIG.ZOOM_LEVELS.BUS_STOP.max) {
    return mapMarkers.value.filter((marker) => marker.type === 'busStop')
  }
  return []
})

const selectedLocationInfo = computed<string>(() => {
  if (!selectedMarker.value) return ''

  const marker = selectedMarker.value
  if (marker.type === 'busStop') {
    const busStop = marker.data as BusStop
    return `【最寄高速バス停】${busStop.busStopName}`
  }
  return ''
})

// 数据和标记处理方法
const createTestData = (): Region[] => {
  return [
    {
      regionId: 1,
      regionName: '関東',
      displayOrder: 1,
      prefectures: [
        {
          prefectureId: 13,
          prefectureName: '東京都',
          displayOrder: 1,
          mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.6917!3d35.6895!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
          areas: [
            {
              areaId: 1,
              areaName: '新宿区',
              displayOrder: 1,
              mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.7001!3d35.6909!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
              busStops: [
                {
                  busStopId: 1,
                  busStopName: '新宿駅西口',
                  busStopShortName: '新宿西口',
                  hasToilet: true,
                  hasWifi: false,
                  mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.6998!3d35.6895!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
                  labels: ['map_device']
                },
                {
                  busStopId: 2,
                  busStopName: '新宿三丁目',
                  busStopShortName: '新宿3丁目',
                  hasToilet: false,
                  hasWifi: true,
                  mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.7065!3d35.6896!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
                  labels: ['map']
                }
              ]
            },
            {
              areaId: 2,
              areaName: '渋谷区',
              displayOrder: 2,
              mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.7016!3d35.6586!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
              busStops: [
                {
                  busStopId: 3,
                  busStopName: '渋谷駅',
                  busStopShortName: '渋谷',
                  hasToilet: true,
                  hasWifi: true,
                  mapInfo: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3281.494062805375!2d139.7016!3d35.6586!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450"></iframe>',
                  labels: ['map_device']
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

const parseCoordinatesFromMapInfo = (mapInfo: string): { lat: number; lng: number } | null => {
  if (!mapInfo) return null

  try {
    const lngMatch = mapInfo.match(/!2d([0-9.-]+)/)
    const latMatch = mapInfo.match(/!3d([0-9.-]+)/)

    if (lngMatch && latMatch) {
      return {
        lng: parseFloat(lngMatch[1]),
        lat: parseFloat(latMatch[1])
      }
    }
  } catch (error) {
    console.error('Failed to parse coordinates from mapInfo:', error)
  }

  return null
}

const createMarkerFromData = (
    data: Prefecture | Area | BusStop,
    type: 'prefecture' | 'area' | 'busStop'
): MapMarker | null => {
  const coords = parseCoordinatesFromMapInfo(data.mapInfo)
  if (!coords) return null

  const getId = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureId
    if (type === 'area') return (data as Area).areaId
    return (data as BusStop).busStopId
  }

  const getName = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureName
    if (type === 'area') return (data as Area).areaName
    return (data as BusStop).busStopName
  }

  return {
    id: `${type}-${getId()}`,
    name: getName(),
    type,
    coordinates: coords,
    data
  }
}

const processMapMarkers = (): void => {
  const markers: MapMarker[] = []

  regions.value.forEach((region) => {
    region.prefectures.forEach((prefecture) => {
      const prefectureMarker = createMarkerFromData(prefecture, 'prefecture')
      if (prefectureMarker) markers.push(prefectureMarker)

      prefecture.areas.forEach((area) => {
        const areaMarker = createMarkerFromData(area, 'area')
        if (areaMarker) markers.push(areaMarker)

        area.busStops.forEach((busStop) => {
          const busStopMarker = createMarkerFromData(busStop, 'busStop')
          if (busStopMarker) markers.push(busStopMarker)
        })
      })
    })
  })

  mapMarkers.value = markers
  updateMarkersOnMap()
}

const loadMapData = async (): Promise<void> => {
  try {
    regions.value = createTestData()
    processMapMarkers()
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}

// Mock Google Map 相关方法
const createMockGoogleMap = () => {
  return {
    setZoom: (zoom: number) => {
      currentZoom.value = zoom
      updateMarkersOnMap()
    },
    getZoom: () => currentZoom.value,
    setCenter: (center: { lat: number; lng: number }) => {
      console.log('Map center set to:', center)
    },
    addListener: (event: string, callback: () => void) => {
      if (event === 'zoom_changed') {
        console.log('Zoom change listener added')
      }
    }
  }
}

const initGoogleMap = async (): Promise<void> => {
  if (!mapContainer.value) return

  try {
    if (USE_MOCK_MAP) {
      googleMap.value = createMockGoogleMap()
    } else {
      const map = new window.google.maps.Map(mapContainer.value, {
        zoom: currentZoom.value,
        center: MAP_CONFIG.DEFAULT_CENTER,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
      })

      googleMap.value = map

      map.addListener('zoom_changed', () => {
        const newZoom = map.getZoom()
        if (newZoom !== currentZoom.value) {
          currentZoom.value = newZoom
          updateMarkersOnMap()
        }
      })
    }

    await loadMapData()
    isMapLoading.value = false
  } catch (error) {
    console.error('Failed to initialize Google Map:', error)
    await loadMapData()
    isMapLoading.value = false
  }
}

const updateMarkersOnMap = (): void => {
  if (USE_MOCK_MAP) {
    console.log(`Updating ${visibleMarkers.value.length} markers for zoom level ${currentZoom.value}`)
    nextTick(() => {
      console.log('Markers updated in template')
    })
  } else {
    // 真实 Google Maps 标记更新代码
  }
}

// 标记点交互方法
const findMarkerByLocation = (location: SelectedLocation): MapMarker | undefined => {
  return mapMarkers.value.find(marker =>
      marker.coordinates.lat === location.coordinates.lat &&
      marker.coordinates.lng === location.coordinates.lng
  )
}

const setLocationData = (marker: MapMarker, locationType: 'departure' | 'destination' | 'waypoint') => {
  const getLocationId = (marker: MapMarker): string => {
    if (marker.type === 'prefecture') {
      return (marker.data as Prefecture).prefectureId.toString()
    } else if (marker.type === 'area') {
      return (marker.data as Area).areaId.toString()
    } else {
      return (marker.data as BusStop).busStopId.toString()
    }
  }

  const locationData: SelectedLocation = {
    name: marker.name,
    id: getLocationId(marker),
    type: marker.type,
    coordinates: marker.coordinates,
    busStopInfo: marker.type === 'busStop' ? selectedLocationInfo.value : undefined
  }

  if (locationType === 'departure') {
    if (selectedLocations.value.departure) {
      const prevMarker = findMarkerByLocation(selectedLocations.value.departure)
      if (prevMarker) prevMarker.role = undefined
    }
    selectedLocations.value.departure = locationData
    formData.value.departure = marker.name
    formData.value.departureId = locationData.id
    formData.value.departureType = marker.type
  } else if (locationType === 'destination') {
    if (selectedLocations.value.destination) {
      const prevMarker = findMarkerByLocation(selectedLocations.value.destination)
      if (prevMarker) prevMarker.role = undefined
    }
    selectedLocations.value.destination = locationData
    formData.value.destination = marker.name
    formData.value.destinationId = locationData.id
    formData.value.destinationType = marker.type
  } else if (locationType === 'waypoint') {
    // 添加到选中的位置
    selectedLocations.value.waypoints.push(locationData)

    // 同步到表单数据
    formData.value.waypoints = selectedLocations.value.waypoints.map((waypoint, index) => ({
      id: `waypoint-${Date.now()}-${index}`,
      location: waypoint.name,
      locationId: waypoint.id,
      locationType: waypoint.type
    }))
  }

  marker.role = locationType
  updateMarkersOnMap()
}

// 事件处理方法
const handleMarkerClick = (marker: MapMarker): void => {
  selectedMarker.value = marker
  showMarkerModal.value = true
}

const handleSetDeparture = (): void => {
  if (!selectedMarker.value) return
  setLocationData(selectedMarker.value, 'departure')
  showMarkerModal.value = false
}

const handleSetDestination = (): void => {
  if (!selectedMarker.value) return
  setLocationData(selectedMarker.value, 'destination')
  showMarkerModal.value = false
}

const handleSetWaypoint = (): void => {
  if (!selectedMarker.value) return

  // 设置位置数据（这会更新 selectedLocations 和 formData）
  setLocationData(selectedMarker.value, 'waypoint')

  // 重要：触发表单数据变化，通知子组件更新
  nextTick(() => {
    // 直接传递更新后的 formData 给子组件
    handleFormChange({
      ...formData.value,
      // 强制触发 waypoints 更新
      waypoints: [...formData.value.waypoints]
    })
  })

  showMarkerModal.value = false
}

const loadGoogleMapsAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GOOGLE_MAPS_API_KEY}&libraries=geometry&loading=async`
    script.async = true
    script.defer = true

    window.initMap = () => {
      resolve()
    }

    script.onload = () => {
      resolve()
    }

    script.onerror = () => {
      reject(new Error('Failed to load Google Maps API'))
    }

    document.head.appendChild(script)
  })
}

// SearchForm 相关的事件处理方法
const handleFormChange = (newFormData: SearchFormData) => {
  // 同步表单数据，但保持地图设置的位置信息
  const updatedFormData = {
    ...formData.value,
    ...newFormData,
    // 保持地图设置的位置信息，除非新数据中有明确的值
    departure: newFormData.departure || formData.value.departure,
    departureId: newFormData.departureId || formData.value.departureId,
    departureType: newFormData.departureType || formData.value.departureType,
    destination: newFormData.destination || formData.value.destination,
    destinationId: newFormData.destinationId || formData.value.destinationId,
    destinationType: newFormData.destinationType || formData.value.destinationType
  }

  // 删除这个条件判断，直接使用子组件传来的waypoints数据
  // 注释掉下面的代码块：
  /*
  if (selectedLocations.value.waypoints.length > 0) {
    updatedFormData.waypoints = selectedLocations.value.waypoints.map((waypoint, index) => ({
      id: waypoint.id,
      location: waypoint.name,
      locationId: waypoint.id,
      locationType: waypoint.type
    }))
  }
  */

  // 当子组件的经由地数量减少时，同步更新selectedLocations
  if (newFormData.waypoints.length < selectedLocations.value.waypoints.length) {
    selectedLocations.value.waypoints = selectedLocations.value.waypoints.slice(0, newFormData.waypoints.length)
  }

  formData.value = updatedFormData
}

const handleSearch = async (searchData: SearchFormData): Promise<void> => {
  console.log('搜索数据:', searchData)
  console.log('出发地详情:', {
    name: searchData.departure,
    id: searchData.departureId,
    type: searchData.departureType
  })
  console.log('目的地详情:', {
    name: searchData.destination,
    id: searchData.destinationId,
    type: searchData.destinationType
  })
  console.log('经由地详情:', searchData.waypoints)

  // 页面跳转
  await navigateTo({
    path: '/search-results',
    query: {
      data: JSON.stringify(searchData)
    }
  })
}

const handleBack = (): void => {
  navigateTo(-1)
}

const togglePanel = (): void => {
  isPanelExpanded.value = !isPanelExpanded.value
}

// Mock 地图位置计算（临时使用）
const getMarkerPosition = (marker: MapMarker) => {
  const positions: Record<string, { left: string; top: string }> = {
    'prefecture-13': { left: '200px', top: '100px' },
    'area-1': { left: '220px', top: '120px' },
    'area-2': { left: '240px', top: '140px' },
    'busStop-1': { left: '225px', top: '125px' },
    'busStop-2': { left: '245px', top: '135px' },
    'busStop-3': { left: '245px', top: '145px' }
  }
  return positions[marker.id] || { left: '150px', top: '150px' }
}

const getMarkerClass = (marker: MapMarker) => {
  return {
    'marker-departure': marker.role === 'departure',
    'marker-destination': marker.role === 'destination',
    'marker-waypoint': marker.role === 'waypoint'
  }
}

// 监听器
watch(currentZoom, () => {
  updateMarkersOnMap()
})

// 生命周期
onMounted(async (): Promise<void> => {
  isMapLoading.value = true
  try {
    if (!USE_MOCK_MAP) {
      await loadGoogleMapsAPI()
      await nextTick()
    }
    await initGoogleMap()
  } catch (error) {
    console.error('Failed to initialize map:', error)
    await loadMapData()
    isMapLoading.value = false
  }
})
</script>
<template>
  <div class="map-search-container">
    <!-- 顶部导航栏 -->
    <div class="header-section">
      <div class="header-content">
        <v-btn icon variant="text" size="small" @click="handleBack">
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>

        <div class="header-title">
          <h3>地図から検索</h3>
        </div>

        <div class="header-spacer"></div>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-section">
      <div class="map-container" ref="mapContainer">
        <!-- Mock地图背景 -->
        <div class="temp-map-background">
          <div class="map-instruction">
            地図を操作して、地点を選择してください。
          </div>

          <!-- Mock标记点 -->
          <div
              v-for="marker in visibleMarkers"
              :key="marker.id"
              class="temp-marker"
              :style="getMarkerPosition(marker)"
              @click="handleMarkerClick(marker)"
          >
            <div class="marker-dot" :class="getMarkerClass(marker)"></div>
            <div class="marker-label">{{ marker.name }}</div>
          </div>

          <!-- 缩放控制按钮 -->
          <div class="zoom-controls">
            <v-btn
                icon
                size="small"
                variant="elevated"
                @click="currentZoom = Math.min(18, currentZoom + 1)"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
            <div class="zoom-level">{{ currentZoom }}</div>
            <v-btn
                icon
                size="small"
                variant="elevated"
                @click="currentZoom = Math.max(5, currentZoom - 1)"
            >
              <v-icon>mdi-minus</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部详情面板 -->
    <div class="detail-panel" :class="{ expanded: isPanelExpanded }">
      <!-- 面板切换按钮 -->
      <div class="panel-header" @click="togglePanel">
        <span class="panel-title">選択中の内容を確認・検索</span>
        <v-btn icon variant="text" size="small" class="toggle-button">
          <v-icon>
            {{ isPanelExpanded ? 'mdi-chevron-down' : 'mdi-chevron-up' }}
          </v-icon>
        </v-btn>
      </div>

      <!-- 面板内容 -->
      <div v-if="isPanelExpanded" class="panel-content">
        <OneWayRoundTripRouteSelect
            :departure-config="{
            value: formData.departure,
            readonly: true,
            showMapButton: false
          }"
            :destination-config="{
            value: formData.destination,
            readonly: true,
            showMapButton: false
          }"
            :waypoint-config="{
            readonly: true,
            showMapButton: false
          }"
            :initial-form-data="formData"
            @search="handleSearch"
            @form-change="handleFormChange"
        />
      </div>
    </div>

    <!-- 标记点详情模态框 -->
    <v-dialog v-model="showMarkerModal" max-width="400" class="marker-modal">
      <v-card v-if="selectedMarker" class="marker-card">
        <v-card-title class="modal-header">
          <v-chip rounded variant="outlined" size="small" class="type-tag">
            {{
              selectedMarker.type === 'busStop'
                  ? '停留所'
                  : selectedMarker.type === 'area'
                      ? 'エリア'
                      : '都道府県'
            }}
          </v-chip>

          <v-btn
              icon
              variant="text"
              size="small"
              @click="showMarkerModal = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="modal-content">
          <div class="location-info">
            <h4 class="location-name">{{ selectedMarker.name }}</h4>
            <p v-if="selectedLocationInfo" class="location-detail">
              {{ selectedLocationInfo }}
            </p>
          </div>

          <div class="action-buttons">
            <v-btn
                color="primary"
                rounded="false"
                class="action-button"
                @click="handleSetDeparture"
            >
              出発地に設定
            </v-btn>

            <v-btn
                color="primary"
                rounded="false"
                class="action-button"
                @click="handleSetDestination"
            >
              到着地に設定
            </v-btn>

            <v-btn
                color="primary"
                rounded="false"
                class="action-button"
                @click="handleSetWaypoint"
            >
              経由地に設定
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- 地图加载中状态 -->
    <div v-if="isMapLoading" class="loading-overlay">
      <v-progress-circular
          indeterminate
          color="primary"
          size="64"
          class="loading-spinner"
      ></v-progress-circular>
      <p class="loading-text">地図を読み込み中...</p>
    </div>
  </div>
</template>
<style scoped lang="scss">
.map-search-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #b2d1ff;
  position: relative;
  overflow: hidden;
}

/* 顶部导航 */
.header-section {
  background: #ffffff;
  padding: 0 8px;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 0 auto;
}

.header-title {
  flex: 1;
  text-align: center;
}

.header-title h3 {
  color: #26499d;
  font-size: 20px;
  font-weight: 400;
  margin: 0;
}

.header-spacer {
  width: 40px;
}

/* 地图区域 */
.map-section {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
}

.map-instruction {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  color: #3b3b3b;
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 12px;
  border-radius: 4px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 临时地图背景 */
.temp-map-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 50%, #a8d8b9 100%);
  position: relative;
  border-radius: 5px 5px 0 0;
}

/* 临时标记点 */
.temp-marker {
  position: absolute;
  display: flex;
  flex-direction: row;
  gap: 3px;
  align-items: center;
  cursor: pointer;
  z-index: 20;
  transition: transform 0.2s ease;
}

.temp-marker:hover {
  transform: scale(1.1);
}

.marker-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #26499d;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.marker-dot.marker-departure {
  background: #4caf50;
}

.marker-dot.marker-destination {
  background: #f44336;
}

.marker-dot.marker-waypoint {
  background: #ff9800;
}

.marker-label {
  font-size: 11px;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
}

/* 缩放控制 */
.zoom-controls {
  position: absolute;
  right: 10px;
  top: 50px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  z-index: 15;
}

.zoom-level {
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

/* 底部面板 */
.detail-panel {
  background: #ffffff;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -4px 10px 0 rgba(33, 65, 88, 0.12);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(calc(100% - 50px));
  z-index: 1000;
}

.detail-panel.expanded {
  transform: translateY(0);
}

.panel-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 11px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 50px;
}

.panel-title {
  flex: 1;
  text-align: center;
  color: #7d7d7d;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.toggle-button {
  position: absolute;
  right: 0;
}

.panel-content {
  padding: 16px;
  max-height: 350px;
  overflow-y: auto;
}

/* 标记点详情模态框 */
.marker-modal :deep(.v-dialog) {
  margin: 16px;
}

.marker-card {
  border-radius: 10px;
  border: 1px solid #9cbcd4;
  overflow: hidden;
  background: #ffffff;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 11px 14px;
  border-bottom: none;
}

.type-tag {
  background: #ffffff;
  border: 1px solid #7d7d7d;
  padding: 5px 10px;
  font-size: 12px;
}

.modal-content {
  padding: 0 14px 11px 14px;
}

.location-info {
  margin-bottom: 9px;
}

.location-name {
  color: #000000;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.location-detail {
  color: #7d7d7d;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  background: #26499d !important;
  height: 49px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff !important;
}

.action-button:hover {
  background: #1e3d8a !important;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(178, 209, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-text {
  margin-top: 16px;
  color: #26499d;
  font-size: 16px;
  font-weight: 500;
}

.loading-spinner {
  width: 64px;
  height: 64px;
}

/* 响应式设计 */
@media (min-width: 960px) {
  .map-search-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .detail-panel {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%) translateY(calc(100% - 50px));
  }

  .detail-panel.expanded {
    transform: translateX(-50%) translateY(0);
  }

  .panel-content {
    max-height: 500px;
  }
}

@media (max-width: 375px) {
  .header-section {
    padding: 8px 12px;
  }

  .panel-content {
    padding: 12px;
  }
}

/* 辅助功能支持 */
.map-container:focus-within {
  outline: 2px solid #26499d;
  outline-offset: 2px;
}

/* 高对比度支持 */
@media (prefers-contrast) {
  .marker-card {
    border-width: 2px;
  }

  .action-button {
    border: 2px solid #ffffff;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .detail-panel,
  .temp-marker {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }
}
</style>
