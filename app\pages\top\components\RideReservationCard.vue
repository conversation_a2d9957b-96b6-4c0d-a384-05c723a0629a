<script setup lang="ts">
import { ref, nextTick } from 'vue';
import RideReservationTicket from "~/pages/top/components/RideReservationTicket.vue";
import { useDisplay } from 'vuetify'
const { mobile } = useDisplay()

// 定义行程数据类型
interface Trip {
  id: string;
  start: string;
  end: string;
  next: string;
  final: string;
  tripHeader: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  startLocation: string;
  endLocation: string;
  isRouteExpanded?: boolean;
  isContentVisible?: boolean;
}

// 定义预约数据类型
interface Ticket {
  id: string;
  arr: Trip[];
}

// 为props添加类型定义
const props = defineProps<{
  tickets: Ticket[];
}>();

// 为ref变量添加类型定义
const activeTripIndexes = ref<number[]>(props.tickets.map(() => 0));
const currentPage = ref<number>(1);

// 为函数参数添加类型
const switchPage = async (page: number) => {
  currentPage.value = page;
  console.log(`切换到第 ${page} 页`);

  await nextTick();
  const scrollContainer = document.querySelector<HTMLDivElement>('.ticket-scroll-container');
  const targetCard = scrollContainer?.children[page - 1] as HTMLElement | undefined;
  if (targetCard && scrollContainer) {
    scrollContainer.scrollTo({
      left: targetCard.offsetLeft - 16,
      behavior: 'smooth'
    });
  }
};

const nextPage = () => {
  if (currentPage.value < props.tickets.length) {
    switchPage(currentPage.value + 1);
  } else {
    switchPage(1);
  }
};

const toggleRouteExpand = (trip: Trip | undefined) => {
  if (trip) {
    trip.isRouteExpanded = !trip.isRouteExpanded;
    trip.isContentVisible = !trip.isContentVisible;
    console.log(`路线展开状态：${trip.isRouteExpanded}，内容显示状态：${trip.isContentVisible}`);
  }
};

const showTicket = (trip: Trip | undefined, reservationId: string) => {
  console.log(`显示【${trip?.tripHeader}】乘车券，预约号：${reservationId}`);
};

const showNextTrip = (resIndex: number) => {
  const reservation = props.tickets[resIndex];
  if (!reservation || !reservation.arr) return;
  
  const arrLength = reservation.arr.length;
  if (activeTripIndexes.value && typeof activeTripIndexes.value[resIndex] === 'number') {
    activeTripIndexes.value[resIndex] = (activeTripIndexes.value[resIndex] + 1) % arrLength;
  }

  if (currentPage.value !== resIndex + 1) {
    currentPage.value = resIndex + 1;
    const scrollContainer = document.querySelector<HTMLDivElement>('.ticket-scroll-container');
    const targetCard = scrollContainer?.children[resIndex] as HTMLElement | undefined;
    if (targetCard && scrollContainer) {
      scrollContainer.scrollTo({
        left: targetCard.offsetLeft - 16,
        behavior: 'smooth'
      });
    }
  }
};

const confirmOrChangeReservation = (trip: Trip | undefined, reservationId: string) => {
  console.log(`处理【${trip?.tripHeader}】预约变更，预约号：${reservationId}`);
};

const prevPage = () => {
  if (currentPage.value > 1) {
    switchPage(currentPage.value - 1);
  } else {
    switchPage(props.tickets.length);
  }
};
</script>
    

<template>
  <!-- 手机端 -->
  <div class="ticket-container" v-show="tickets.length && mobile">
    <h2 class="section-title">直近の乗車予定</h2>
    <div class="title-underline"></div>

    <div class="ticket-scroll-container" :class="{ 'center-container': tickets.length === 1 }">
      <div class="ticket-card" v-for="(reservation, resIndex) in tickets" :key="reservation.id">


        <div v-if="reservation.arr.length" class="trip-item-wrapper">
          <template v-if="typeof activeTripIndexes[resIndex] === 'number' && reservation.arr[activeTripIndexes[resIndex]]">
            <div class="route">
              <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.start }}</span>
              <img src="~/assets/image/triangle.png" class="trigicon" alt="画像の読み込みに失敗しました" />
              <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.end }}</span>
              <img src="~/assets/image/triangle.png" class="trigicon" alt="画像の読み込みに失敗しました" />
              <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.next }}</span>
              <img src="~/assets/image/triangle.png" class="trigicon" alt="画像の読み込みに失敗しました" />
              <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.final }}</span>
              <button class="route-expand-btn" @click="toggleRouteExpand(reservation.arr[activeTripIndexes[resIndex]])">
                <img src="~/assets/image/Icon.png" class="chevron-icon" alt="画像の読み込みに失敗しました"
                  :class="{ 'rotate-180': reservation.arr[activeTripIndexes[resIndex]]?.isRouteExpanded }" />
              </button>
            </div>

            <div class="reservation-number">予約番号：{{ reservation.id }}</div>

            <div v-if="reservation.arr[activeTripIndexes[resIndex]]?.isContentVisible">
              <div class="trip-item">
                <div class="trip-header">{{ reservation.arr[activeTripIndexes[resIndex]]?.tripHeader }}</div>
                <div class="trip-date">
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.startDate }}</span>
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.endDate }}</span>
                </div>
                <div class="trip-time">
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.startTime }}</span>
                  <span class="line"> - </span>
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.endTime }}</span>
                </div>
                <div class="trip-location">
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.startLocation }}</span>
                  <span>{{ reservation.arr[activeTripIndexes[resIndex]]?.endLocation }}</span>
                </div>
                <button class="show-ticket-btn"
                  @click="showTicket(reservation.arr[activeTripIndexes[resIndex]], reservation.id as string)">
                  乗車券を表示
                  <img src="~/assets/image/ticket.png" class="ticketIcon" alt="画像の読み込みに失敗しました" />
                </button>
              </div>
              <div class="next-trip" @click="showNextTrip(resIndex)">
                次の便
                <img src="~/assets/image/Icon.png" class="nextIcon" alt="画像の読み込みに失敗しました" />
              </div>
              <div class="reservation-actions"
                @click="confirmOrChangeReservation(reservation.arr[activeTripIndexes[resIndex]], reservation.id as string)">
                <span>予約確認・変更</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="page-nav">
      <img src="~/assets/image/Icon.png" class="prevIcon" @click="prevPage" alt="前のページへ" />
      <button v-for="page in tickets.length" :key="page" class="page-btn" :class="{ 'active': currentPage === page }"
        @click="switchPage(page)">
        {{ page }}
      </button>
      <img src="~/assets/image/Icon.png" class="nextIcon" @click="nextPage" alt="画像の読み込みに失敗しました" />
    </div>
  </div>

  <!-- 没有卡片时 -->
  <div class="reservation-card" v-if="!tickets.length && mobile">
    <div class="header">
      <h2 class="title">直近の乗車予定</h2>
    </div>
    <div class="content">
      <p class="message">乗車前の予約はありません</p>
    </div>
  </div>
  <!-- pc端 -->
  <div v-show="!mobile">
    <RideReservationTicket  :tickets="tickets"/>
  </div>
</template>



<style lang="scss" scoped>
.prevIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(270deg);
  transition: transform 0.3s ease;
}

.reservation-card {
  background-color: white;
  width: 85%;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  border: 1px solid #f3f4f6;
}

.header {
  background-color: #eff6ff;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.title {
  color: #26499d;
  font-weight: 500;
  font-size: 18px;
  text-align: center;
  margin: 0;
}

.content {
  padding: 32px 24px;
  text-align: center;
}

.message {
  color: #4b5563;
  margin: 0;
}

.trigicon {
  width: 13px;
  height: 8px;
  margin: 0 8px;
}

.ticket-container {
  padding: 20px;
  overflow-x: auto;
}

.section-title {
  text-align: center;
  color: #26499d;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

.ticket-scroll-container {
  display: flex;
  overflow-x: auto;
  gap: 16px;
  padding-bottom: 8px;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &.center-container {
    justify-content: center;
  }
}

.ticket-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 300px;
  flex: 0 0 auto;
  height: fit-content;
}

.route {
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 15px;
  background-color: #E7F2FA;
  height: 50px;
  border-radius: 8px 8px 0 0;
}

.route-expand-btn {
  background: none;
  border: none;
  padding: 15px;
  margin-left: 8px;
}

.chevron-icon {
  display: inline-block;
  transition: transform 0.3s ease;
  width: 24px;
  height: 24px;
  color: #2d5199;
  margin-top: 2px;
}

.rotate-180 {
  transform: rotate(180deg);
}

.reservation-number {
  font-size: 14px;
  text-align: center;
  color: #26499D;
  margin: 16px 0 0 0;
}

.trip-item {
  background-color: #f3faff;
  border-radius: 6px;
  border: 1px solid #acd1ed;
  padding-bottom: 16px;
  margin: 15px;
}

.trip-header {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #26499D;
  background-color: #ACD1ED;
  padding: 4px 0;
  border-radius: 4px 4px 0 0;
  height: 33px;
}

.trip-date {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #555;
  padding: 18px 25px 6px 25px;
}

.trip-time {
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  font-weight: bold;
  color: #26499d;
  padding: 0 25px 6px 25px;

  .line {
    width: 14px;
    height: 5px;
    color: #ccc;
  }
}

.trip-location {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #555;
  padding: 0 25px 16px 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.show-ticket-btn {
  background-color: #2d5199;
  color: #fff;
  border-radius: 40px;
  padding: 16px 0px;
  font-size: 14px;
  transition: background-color 0.3s ease;
  width: 230px;
  height: 50px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;

  .ticketIcon {
    width: 22px;
    height: 22px;
    margin-left: 10px;
  }
}

.show-ticket-btn:hover {
  background-color: #22407d;
}

.next-trip {
  font-size: 14px;
  color: #26499D;
  text-align: right;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transition: color 0.3s ease;
  margin-right: 20px;
}

.nextIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.next-trip span {
  margin-left: 5px;
  font-size: 16px;
}

.reservation-actions {
  font-size: 14px;
  color: #26499d;
  text-align: center;
  transition: background-color 0.3s ease;
  border-top: 1px solid #9cbcd4;
  padding: 16px 20px;

  span {
    border-bottom: 1px solid #26499d;
  }
}

.page-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.page-btn {
  width: 30px;
  height: 30px;
  color: #26499D;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.page-btn.active {
  background-color: #E7F2FA;
  color: #26499D;
  border-color: #2d5199;
  border-radius: 50%;
}
</style>