import { defineStore } from 'pinia';
import type { TouristSpot } from '~/types/edit';

// 经由地
export const useWaypointStore = defineStore('waypoint', {
  state: () => ({
    waypoints: [] as TouristSpot[],
  }),
  getters: {
    // 避免引用问题
    getAllWaypoints: (state) => {
      return [...state.waypoints];
    },
    getWaypointCount: (state) => state.waypoints.length,
  },
  actions: {
    // 添加经由地（避免重复添加）
    addWaypoint(data: TouristSpot) {
      // 检查是否已存在相同ID和名称的经由地
      const exists = this.waypoints.some(waypoint => 
        waypoint.id === data.id && waypoint.name === data.name
      );
      
      if (!exists) {
        // 使用深拷贝避免引用问题
        this.waypoints.push({ ...data });
      }
    },
    
    // 更新指定位置的经由地
    updateWaypoint(index: number, data: TouristSpot) {
      if (index >= 0 && index < this.waypoints.length) {
        this.waypoints[index] = { ...data };
      }
    },
    
    // 删除指定索引的经由地
    removeWaypoint(index: number) {
      if (index >= 0 && index < this.waypoints.length) {
        const removed = this.waypoints.splice(index, 1)[0];
      }
    },
    
    // 根据ID删除经由地
    removeWaypointById(id: number) {
      const index = this.waypoints.findIndex(waypoint => waypoint.id === id);
      if (index !== -1) {
        const removed = this.waypoints.splice(index, 1)[0];
      }
    },
    
    // 清空所有经由地
    clearAllWaypoints() {
      const count = this.waypoints.length;
      this.waypoints = [];
    },
    
    // 设置经由地列表（替换整个列表）
    setWaypoints(waypoints: TouristSpot[]) {
      this.waypoints = waypoints.map(item => ({ ...item }));
    }
  }
});

// 始发地 
export const useOriginStore = defineStore('origin', {
  state: () => ({
    currentOriginData: null as TouristSpot | null,
  }),
  getters: {
    getSelectedOrigin: (state) => {
      return state.currentOriginData;
    }
  },
  actions: {
    setCurrentOriginData(data: TouristSpot) {
      this.currentOriginData = { ...data };
    },
    clearCurrentOriginData() {
      const prevName = this.currentOriginData?.name;
      this.currentOriginData = null;
    }
  }
});

// 到着地 
export const useSpotStore = defineStore('tourist-facilities', {
  state: () => ({
    currentTouristData: null as TouristSpot | null,
  }),
  getters: {
    getSelectedSpot: (state) => {
      return state.currentTouristData;
    }
  },
  actions: {
    setCurrentTouristData(data: TouristSpot) {
      this.currentTouristData = { ...data };
    },
    clearCurrentTouristData() {
      const prevName = this.currentTouristData?.name;
      this.currentTouristData = null;
    }
  }
});
