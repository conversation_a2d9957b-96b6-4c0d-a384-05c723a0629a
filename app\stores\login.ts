import { defineStore } from 'pinia'
import type { LoginData, LoginRes, UserState } from '~/types/login'
import { login as userLogin } from '~/composables/useLogin'
import { clearToken } from '~/utils/auth'
import LocalCache, { StorageType } from '~/utils/cache'
// import { decrypt, encrypt } from '~/utils/crypto'

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user_name: undefined,
    email: undefined,
    user_id: undefined
  }),

  getters: {
    userProfile(state: UserState): UserState {
      return { ...state }
    }
  },

  actions: {
    setInfo(partial: LoginRes) {
      this.user_id = partial.userId
      this.user_name = partial.userName
    },

    resetInfo() {
      this.$reset()
    },

    async login(loginForm: LoginData) {
      const result = await userLogin(loginForm)
      const token = result?.data?.accessToken
      if (token) {
        const storageType = loginForm.remember
          ? StorageType.LOCAL
          : StorageType.SESSION
        LocalCache.setCache('ConemobiToken', token, storageType)
        this.setInfo(result?.data)
      }
      return result
    },

    async logout() {
      this.resetInfo()
      clearToken()
    }
  }

  //   persist: {
  //     key: 'u',
  //     storage: localStorage,
  //     pick: ['user_name', 'company_name', 'role'] as (keyof UserState)[],
  //     serializer: {
  //       serialize: (value: Partial<UserState>) => {
  //         return encrypt(value)
  //       },
  //       deserialize: (value: string) => {
  //         return decrypt(value) || {}
  //       },
  //     },
  //   },
})
