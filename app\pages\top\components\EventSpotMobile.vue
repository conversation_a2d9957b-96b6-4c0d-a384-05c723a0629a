<template>
  <div class="horizontal-scroll">
    <!-- 滚动容器：限制可视区域，内部内容横向滚动 -->
    <div
        class="scroll-container"
        ref="scrollRef"
        @mousedown="startDrag"
        @wheel="handleWheel"
    >
      <!-- 滚动内容：横向排列所有item -->
      <div class="scroll-content">
        <div
            class="scroll-item"
            v-for="(item, index) in props.items"
            :key="item.eventId || index"
            @click="handleItemClick(item)"
        >
          <div class="item-img-wrap">
            <img
                :src="item.image"
                :alt="`${item.name}-${index}`"
                class="item-img"
                @error="handleImgError($event)"
            />
          </div>


          <!-- 文本区域：固定高度，避免内容溢出 -->
          <div class="item-text">
            <h3 class="item-title">{{ item.name }}</h3>
            <p style="border: 1px solid #c1d5e4; height: 1px;width: 24px;"></p>
            <p class="item-desc">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watchEffect, defineProps, defineEmits } from "vue";

// eventId: 101,
//     name: "春の桜祭り",
//     prefectureId: 13,
//     address: "東京都千代田区皇居外苑",
//     startDate: "2025-03-20",
//     endDate: "2025-04-10",
//     image: "https://picsum.photos/400/300?random=1"

const props = defineProps({
  items: {
    type: Array,
    required: true,
  }
});

// Define emits for parent component communication
const emit = defineEmits(['item-click']);

// 滚动容器引用
const scrollRef = ref(null);
// 拖动相关状态
const isDragging = ref(false);
const startX = ref(0);
const startScrollLeft = ref(0);
// 是否有横向滚动空间（控制提示显示）
const hasScroll = ref(false);

// 监听滚动容器尺寸，判断是否需要滚动
watchEffect(() => {
  if (scrollRef.value) {
    const { scrollWidth, clientWidth } = scrollRef.value;
    // 内容总宽度 > 容器可视宽度 → 有滚动空间
    hasScroll.value = scrollWidth > clientWidth + 10; // +10 避免精度误差
  }
});

// Handle item click
const handleItemClick = (item) => {
  // Don't trigger click during dragging
  // if (isDragging.value) return;

  // Emit click event with item data
  if (item.event_id) {
    emit('item-click', item.event_id);
  } else if (item.id) {
    emit('item-click', item.id);
  } else {
    emit('item-click', item);
  }
};

// 开始拖动（鼠标按下）
const startDrag = (e) => {
  isDragging.value = true;
  // 记录初始位置：鼠标X坐标 - 容器偏移量
  startX.value = e.pageX - scrollRef.value.offsetLeft;
  // 记录初始滚动距离
  startScrollLeft.value = scrollRef.value.scrollLeft;
  // 改变鼠标样式
  scrollRef.value.style.cursor = "grabbing";
  // 绑定全局鼠标移动/松开事件
  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("mouseup", endDrag);
};

// 处理拖动（鼠标移动）
const handleDrag = (e) => {
  if (!isDragging.value) return;
  e.preventDefault(); // 阻止文本选中、页面纵向滚动等默认行为
  // 计算当前鼠标X坐标相对容器的位置
  const currentX = e.pageX - scrollRef.value.offsetLeft;
  // 计算滚动距离（拖动速度系数1.2，优化手感）
  const scrollDistance = (currentX - startX.value) * 1.2;
  // 更新滚动位置
  scrollRef.value.scrollLeft = startScrollLeft.value - scrollDistance;
};

// 结束拖动（鼠标松开）
const endDrag = () => {
  // Small delay to prevent click event after drag
  setTimeout(() => {
    isDragging.value = false;
  }, 100);

  scrollRef.value.style.cursor = "grab";
  // 移除全局事件监听
  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("mouseup", endDrag);
};

// 滚轮控制横向滚动（替代纵向滚动）
const handleWheel = (e) => {
  e.preventDefault(); // 阻止页面纵向滚动
  // 滚轮方向映射：向下滚→向右滚，向上滚→向左滚
  scrollRef.value.scrollBy({
    left: e.deltaY > 0 ? 150 : -150, // 每次滚动150px，可调整速度
    behavior: "smooth", // 平滑滚动效果
  });
};

// 图片加载失败：显示默认图
const handleImgError = (e) => {
  // 灰度模糊默认图，避免空白
  e.target.src = "https://picsum.photos/400/200?grayscale&blur=2";
  e.target.alt = "画像が読み込めませんでした";
};
</script>

<style scoped>
.horizontal-scroll {
  width: 100%;
  box-sizing: border-box;
}

.scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  cursor: grab;

  &::-webkit-scrollbar {
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
  }
  scrollbar-width: thin;
  scrollbar-color: #ddd #f5f5f5;
}

.scroll-content {
  display: flex;
  gap: 20px;
  padding: 10px 0;
  margin-left: 10px;
  margin-right: 10px;
}

.scroll-item {
  width: 280px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(0);
  }
}

.item-img-wrap {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.item-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.scroll-item:hover .item-img {
  transform: scale(1.05);
}

.item-text {
  padding: 0 16px 16px;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.item-desc {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

</style>