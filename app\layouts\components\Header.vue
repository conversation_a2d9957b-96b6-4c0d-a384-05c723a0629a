<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { isLogin } from '~/utils/auth'
import headerImageUrl from '~/assets/image/header.png'
import menuImageUrl from '~/assets/image/Menu.png'
import type { MenuItem } from '~/types/header'

const router = useRouter()

// 响应式状态
const leftDrawer = ref(false)
const rightDrawer = ref(false)
const isLoggedIn = computed(() => !isLogin())

// 静态资源
const headerImage = headerImageUrl
const menuImage = menuImageUrl

// 过滤后的菜单项
const filteredLeftMenuItems = computed(() =>
  getFilteredMenuItems(leftMenuItems, isLoggedIn.value)
)

const filteredUserMenuItems = computed(() =>
  getFilteredMenuItems(userMenuItems, isLoggedIn.value)
)

/**
 * 切换左侧抽屉菜单
 */
const toggleLeftDrawer = () => {
  // 如果右侧抽屉打开，先关闭右侧抽屉
  if (rightDrawer.value) {
    rightDrawer.value = false
  }
  leftDrawer.value = !leftDrawer.value
}

/**
 * 切换右侧抽屉菜单（仅登录用户可用）
 */
const toggleRightDrawer = () => {
  if (isLoggedIn.value) {
    // 如果左侧抽屉打开，先关闭左侧抽屉
    if (leftDrawer.value) {
      leftDrawer.value = false
    }
    rightDrawer.value = !rightDrawer.value
  } else {
    // 未登录时跳转到登录页面
    router.push('/login')
  }
}

/**
 * 处理菜单项点击
 * 只有直接链接项目才会触发路由跳转
 * 分组项目（有children的）不会触发跳转，只负责展开/收起
 */
const handleMenuItemClick = (item: MenuItem) => {
  // 只有没有子菜单的项目才执行导航或动作
  if (!item.children) {
    if (item.action) {
      item.action()
    } else if (item.path) {
      router.push(item.path)
      // 点击菜单项后关闭抽屉
      leftDrawer.value = false
      rightDrawer.value = false
    }
  }
  // 有子菜单的项目不做任何操作，让 v-list-group 自己处理展开/收起
}

/**
 * 处理登出
 */
const handleLogout = () => {
  // 这里调用登出逻辑
  // 可以从 auth.ts 中导入登出函数
  router.push('/logout')
  leftDrawer.value = false
}

/**
 * 导航到FAQ页面
 */
const navigateToFAQ = () => {
  router.push('/faq')
}

/**
 * 导航到联系我们页面
 */
const navigateToContact = () => {
  router.push('/contact')
}

// 左侧菜单配置（根据第2张图片）
const userMenuItems: MenuItem[] = [
  {
    id: 'appointment-confirm-change',
    title: '予約確認・変更・キャンセル',
    icon: 'mdi-calendar-edit',
    path: '/appointment-confirm-change'
  },
  {
    id: 'saved-routes',
    title: '一時保存した型を見る',
    icon: 'mdi-bookmark-outline',
    path: '/saved-routes'
  },
  {
    id: 'favorite-routes',
    title: 'お気に入りルート',
    icon: 'mdi-heart-outline',
    path: '/favorite-routes'
  },
  {
    id: 'bus-navi-map',
    title: 'バスなびマップ',
    icon: 'mdi-map-outline',
    path: '/bus-navi-map'
  },
  {
    id: 'coupon-list',
    title: 'クーポン一覧',
    icon: 'mdi-ticket-percent-outline',
    path: '/coupon-list'
  },
  {
    id: 'registration-info',
    title: '登録情報確認・変更',
    icon: 'mdi-account-edit-outline',
    children: [
      {
        id: 'member-info',
        title: '会員情報',
        path: '/member-info'
      },
      {
        id: 'favorite-bus-stops',
        title: 'お気に入りバス停',
        path: '/favorite-bus-stops'
      },
      {
        id: 'favorite-bus-companies',
        title: 'お気に入りバス会社',
        path: '/favorite-bus-companies'
      }
    ]
  }
]

// 右侧用户菜单配置（根据第1张图片）
const leftMenuItems: MenuItem[] = [
  // 分组：初めての方へ
  {
    id: 'for-beginners',
    title: '初めての方へ',
    children: [
      {
        id: 'about-konemobi',
        title: 'コネモビとは',
        path: '/about'
      },
      {
        id: 'reservation-flow',
        title: '予約から乗車までの流れ',
        path: '/reservation-flow'
      },
      {
        id: 'member-service',
        title: '会員サービス',
        path: '/member-service'
      }
    ]
  },
  // 分组：高速バス検索
  {
    id: 'highway-bus-search',
    title: '高速バス検索',
    children: [
      {
        id: 'special-products',
        title: 'お得な商品',
        path: '/special-products'
      },
      {
        id: 'tourist-search',
        title: '観光地・施設から検索',
        path: '/tourist-search'
      },
      {
        id: 'event-search',
        title: '期間限定・イベントから検索',
        path: '/event-search'
      }
    ]
  },
  // 直接链接项目
  {
    id: 'campaign',
    title: 'キャンペーン',
    path: '/campaign'
  },
  {
    id: 'news',
    title: 'お知らせ',
    path: '/news'
  },
  {
    id: 'operation-status',
    title: '運行状況',
    path: '/operation-status'
  },
  // 分组：お問い合わせ
  {
    id: 'inquiry',
    title: 'お問い合わせ',
    children: [
      {
        id: 'faq',
        title: 'FAQ',
        path: '/faq'
      },
      {
        id: 'member-bus-companies',
        title: '加盟バス会社一覧',
        path: '/member-bus-companies'
      },
      {
        id: 'operating-company',
        title: '運営会社（連絡先）',
        path: '/operating-company'
      }
    ]
  }
]

// 根据登录状态过滤菜单项
const getFilteredMenuItems = (
  items: MenuItem[],
  isLoggedIn: boolean
): MenuItem[] => {
  return items.filter((item) => {
    if (item.hide) return false
    // 可以在这里添加更多的过滤逻辑
    return true
  })
}
</script>

<template>
  <div>
    <!-- 顶部导航栏 -->
    <v-app-bar :elevation="0" color="white" height="50" fixed app>
      <v-container class="header" fluid>
        <v-row align="center" no-gutters>
          <!-- 左侧菜单按钮 -->
          <v-col
            xs="1"
            sm="2"
            md="2"
            lg="2"
            xl="2"
            xxl="2"
            class="header-container"
          >
            <v-btn
              icon
              variant="text"
              size="small"
              @click="toggleLeftDrawer"
              class="menu-button"
            >
              <img :src="menuImage" alt="menu" class="logo-menu" />
            </v-btn>
          </v-col>

          <!-- LOGO -->
          <v-col
            xs="10"
            sm="6"
            md="6"
            lg="6"
            xl="6"
            xxl="6"
            class="logo-section"
          >
            <img :src="headerImage" alt="コネモビ" class="logo-image" />
          </v-col>

          <!-- 右侧按钮区域 -->
          <v-col
            xs="1"
            sm="4"
            md="4"
            lg="4"
            xl="4"
            xxl="4"
            class="d-flex justify-end d-md-inline"
            style="text-align: end"
          >
            <span class="text d-none d-md-inline" @click="navigateToFAQ">
              よくある質問
            </span>
            <span class="text d-none d-md-inline" @click="navigateToContact">
              お問い合わせ
            </span>

            <v-btn
              v-if="!isLoggedIn"
              @click="toggleRightDrawer"
              rounded
              color="primary"
              variant="flat"
              size="x-small"
            >
              ログイン
            </v-btn>
            <v-btn
              v-else
              size="x-small"
              prepend-icon="mdi-account"
              @click="toggleRightDrawer"
              stacked
              color="primary"
              style="text-transform: none !important; font-weight: 700"
            >
              Myメニュー
            </v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- 左侧抽屉菜单 -->
    <v-navigation-drawer
      v-model="leftDrawer"
      location="left"
      temporary
      width="300"
      class="left-drawer"
      active-color="primary"
    >
      <v-divider></v-divider>

      <v-list nav density="compact">
        <template v-for="(item, index) in filteredLeftMenuItems" :key="item.id">
          <v-divider v-if="index > 0" class="my-1"></v-divider>

          <!-- 直接链接项目（没有子菜单） -->
          <v-list-item
            v-if="!item.children"
            :prepend-icon="item.icon"
            :title="item.title"
            @click="handleMenuItemClick(item)"
            class="drawer-item direct-link"
          ></v-list-item>

          <!-- 分组项目（有子菜单，显示展开三角图标） -->
          <v-list-group v-else :value="item.id" class="menu-group">
            <template v-slot:activator="{ props }">
              <v-list-item
                v-bind="props"
                :prepend-icon="item.icon"
                :title="item.title"
                class="drawer-item group-header"
                append-icon="mdi-chevron-down"
              ></v-list-item>
            </template>
            <!-- 子菜单项目（有缩进） -->
            <v-list-item
              v-for="child in item.children"
              :key="child.id"
              :title="child.title"
              @click="handleMenuItemClick(child)"
              class="drawer-sub-item"
            ></v-list-item>
          </v-list-group>
        </template>
      </v-list>
    </v-navigation-drawer>

    <!-- 右侧抽屉菜单 -->
    <v-navigation-drawer
      v-model="rightDrawer"
      location="right"
      temporary
      width="300"
      class="right-drawer"
    >
      <v-divider></v-divider>
      <v-list nav density="compact">
        <template v-for="(item, index) in filteredUserMenuItems" :key="item.id">
          <v-divider v-if="index > 0" class="my-1"></v-divider>
          <!-- 直接链接项目（没有子菜单） -->
          <v-list-item
            v-if="!item.children"
            :title="item.title"
            @click="handleMenuItemClick(item)"
            class="drawer-item direct-link"
          ></v-list-item>

          <!-- 分组项目（有子菜单，显示展开三角图标） -->
          <v-list-group v-else :value="item.id" class="menu-group">
            <template v-slot:activator="{ props }">
              <v-list-item
                v-bind="props"
                :title="item.title"
                class="drawer-item group-header"
                append-icon="mdi-chevron-down"
              ></v-list-item>
            </template>

            <!-- 子菜单项目（有缩进） -->
            <v-list-item
              v-for="child in item.children"
              :key="child.id"
              :title="child.title"
              @click="handleMenuItemClick(child)"
              class="drawer-sub-item"
            ></v-list-item>
          </v-list-group>
        </template>

        <!-- 登录状态下的登出选项 -->
        <template v-if="isLoggedIn">
          <v-divider class="my-2"></v-divider>
          <v-list-item
            title="ログアウト"
            @click="handleLogout"
            class="drawer-item logout-item direct-link"
          ></v-list-item>
        </template>
      </v-list>
    </v-navigation-drawer>
  </div>
</template>

<style scoped lang="scss">
.header-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0 0;
  padding: 12px 16px;
}

.logo-menu {
  width: 26px;
  height: 26px;
}

.menu-button {
  margin-right: 16px;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  .logo-image {
    height: 26px;
  }
}

.text {
  font-size: 14px;
  margin-right: 16px;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #26499d;
  }
}

.login-button {
  line-height: 0;
  width: 70px;
  border-radius: 50px;
  font-size: 12px;
}
:deep(.v-btn__prepend) {
  margin-bottom: 0 !important;
}
.v-btn.v-btn--density-default {
  height: 26px;
}

.drawer-item {
  color: rgb(var(--v-theme-primary));
}

// 抽屉样式
.left-drawer,
.right-drawer {
  // 直接链接项目样式
  :deep(.direct-link) {
    transition: all 0.2s ease;
    cursor: pointer;

    .v-list-item-title {
      font-weight: 700 !important;
      font-size: 14px !important;
    }

    &:hover {
      background-color: rgba(38, 73, 157, 0.1);
    }
  }

  :deep(.group-header) {
    transition: all 0.2s ease;
    cursor: pointer;
    font-weight: 700 !important;

    &:hover {
      background-color: rgba(38, 73, 157, 0.05);
    }

    .v-list-item-title {
      font-weight: 700 !important;
      font-size: 14px !important;
    }
  }

  // 子菜单项目样式（有缩进）
  .drawer-sub-item {
    padding: 8px 20px 8px 50px; // 左侧增加缩进
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
    color: rgb(var(--v-theme-primary));

    &:hover {
      background-color: rgba(38, 73, 157, 0.08);
    }
  }

  // 分组容器样式
  .menu-group {
    .v-list-group__items {
      .v-list-item {
        padding-left: 0; // 重置默认左边距
      }
    }
  }

  // 登出按钮特殊样式
  .logout-item {
    color: #d32f2f;

    &:hover {
      background-color: rgba(211, 47, 47, 0.1);
    }
  }

  // 自定义展开图标样式
  .v-list-group--active {
    .group-header {
      .v-list-item__append {
        .v-icon {
          transform: rotate(180deg);
          transition: transform 0.2s ease;
        }
      }
    }
  }

  .group-header {
    .v-list-item__append {
      .v-icon {
        transition: transform 0.2s ease;
      }
    }
  }
}
:deep(.v-container--fluid) {
  max-width: 62%;
}

// 移动端适配
@media (max-width: 768px) {
  :deep(.v-container--fluid) {
    max-width: 100%;
  }
  .left-drawer,
  .right-drawer {
    width: 280px !important;
  }
}
</style>
