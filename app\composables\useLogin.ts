import type { LoginData, LoginRes } from "~/types/login";
import type { BaseResponse } from "~/types";

enum URL {
  login = '/admin-backend/auth/login',
  logout = '/user/logout',
}

const { post } = useHttp()

// ログイン処理
const login = async (data: LoginData): Promise<BaseResponse<LoginRes>> => {
  return post<LoginRes>({ url: URL.login, data })
}

// ログアウト処理
const logout = async (): Promise<BaseResponse<any>> => {
  return post({ url: URL.logout })
}

export { logout, login }
