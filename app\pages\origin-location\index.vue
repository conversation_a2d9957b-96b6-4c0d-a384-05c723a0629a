<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import SearchResult from '../../components/OriginDestHistoryInput.vue'
import { isLogin } from '~/utils/auth'

const isLoggedIn = computed(() => !isLogin())

definePageMeta({
  hideFooter: true
});

interface BusStop {
  bus_stop_id: number;
  bus_stop_name: string;
  bus_stop_short_name: string;
  latitude: number;
  longitude: number;
  has_toilet: boolean;
  has_wifi: boolean;
  labels: string[];
}

interface Area {
  area_id: number;
  area_name: string;
  display_order: number;
  latitude: number;
  longitude: number;
  bus_stops: BusStop[];
}

interface Prefecture {
  prefecture_id: number;
  prefecture_name: string;
  display_order: number;
  latitude: number;
  longitude: number;
  areas: Area[];
}

interface DataItem {
  id: number;
  name: string;
  type: string;
}

const jsonData = {
  data: [
    {
      region_id: 1,
      region_name: "関東",
      display_order: 1,
      prefectures: [
        {
          prefecture_id: 13,
          prefecture_name: "東京都",
          display_order: 1,
          latitude: 35.6895,
          longitude: 139.6917,
          areas: [
            {
              area_id: 1,
              area_name: "新宿区",
              display_order: 1,
              latitude: 35.6938,
              longitude: 139.7034,
              bus_stops: [
                {
                  bus_stop_id: 1,
                  bus_stop_name: "新宿駅西口",
                  bus_stop_short_name: "新宿西口",
                  latitude: 35.6909,
                  longitude: 139.6995,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ["map_device"]
                },
                {
                  bus_stop_id: 2,
                  bus_stop_name: "新宿三丁目",
                  bus_stop_short_name: "新宿3丁目",
                  latitude: 35.6901,
                  longitude: 139.7052,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            },
            {
              area_id: 2,
              area_name: "渋谷区",
              display_order: 2,
              latitude: 35.6617,
              longitude: 139.7040,
              bus_stops: [
                {
                  bus_stop_id: 3,
                  bus_stop_name: "渋谷駅東口",
                  bus_stop_short_name: "渋谷東口",
                  latitude: 35.6580,
                  longitude: 139.7016,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            }
          ]
        },
        {
          prefecture_id: 14,
          prefecture_name: "神奈川県",
          display_order: 2,
          latitude: 35.4478,
          longitude: 139.6425,
          areas: [
            {
              area_id: 3,
              area_name: "横浜市",
              display_order: 1,
              latitude: 35.4437,
              longitude: 139.6380,
              bus_stops: [
                {
                  bus_stop_id: 4,
                  bus_stop_name: "横浜駅西口",
                  bus_stop_short_name: "横浜西口",
                  latitude: 35.4658,
                  longitude: 139.6201,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ["map_device"]
                }
              ]
            }
          ]
        },
        {
          prefecture_id: 12,
          prefecture_name: "千葉県",
          display_order: 3,
          latitude: 35.6048,
          longitude: 140.1235,
          areas: []
        },
        {
          prefecture_id: 11,
          prefecture_name: "埼玉県",
          display_order: 4,
          latitude: 36.1006,
          longitude: 139.6222,
          areas: []
        },
        {
          prefecture_id: 15,
          prefecture_name: "茨城県",
          display_order: 5,
          latitude: 36.3417,
          longitude: 140.4464,
          areas: []
        },
        {
          prefecture_id: 2,
          prefecture_name: "青森県",
          display_order: 1,
          latitude: 40.8244,
          longitude: 140.7400,
          areas: []
        },
        {
          prefecture_id: 3,
          prefecture_name: "岩手県",
          display_order: 2,
          latitude: 39.7036,
          longitude: 141.1527,
          areas: []
        },
        {
          prefecture_id: 4,
          prefecture_name: "宮城県",
          display_order: 3,
          latitude: 38.2689,
          longitude: 140.8721,
          areas: []
        },
        {
          prefecture_id: 5,
          prefecture_name: "秋田県",
          display_order: 4,
          latitude: 39.7181,
          longitude: 140.1034,
          areas: []
        },
        {
          prefecture_id: 23,
          prefecture_name: "愛知県",
          display_order: 1,
          latitude: 35.1802,
          longitude: 136.9066,
          areas: []
        },
        {
          prefecture_id: 21,
          prefecture_name: "長野県",
          display_order: 2,
          latitude: 36.6513,
          longitude: 138.1811,
          areas: []
        },
        {
          prefecture_id: 24,
          prefecture_name: "三重県",
          display_order: 3,
          latitude: 34.9769,
          longitude: 136.5080,
          areas: []
        },
        {
          prefecture_id: 27,
          prefecture_name: "大阪府",
          display_order: 1,
          latitude: 34.6937,
          longitude: 135.5023,
          areas: []
        },
        {
          prefecture_id: 26,
          prefecture_name: "京都府",
          display_order: 2,
          latitude: 35.0116,
          longitude: 135.7681,
          areas: []
        },
        {
          prefecture_id: 28,
          prefecture_name: "兵庫県",
          display_order: 3,
          latitude: 34.7304,
          longitude: 135.1957,
          areas: []
        },
        {
          prefecture_id: 40,
          prefecture_name: "福岡県",
          display_order: 1,
          latitude: 33.5902,
          longitude: 130.4017,
          areas: []
        },
        {
          prefecture_id: 44,
          prefecture_name: "鹿児島県",
          display_order: 2,
          latitude: 31.5602,
          longitude: 130.5581,
          areas: []
        },
        {
          prefecture_id: 1,
          prefecture_name: "北海道",
          display_order: 1,
          latitude: 43.0642,
          longitude: 141.3468,
          areas: []
        },
        {
          prefecture_id: 32,
          prefecture_name: "香川県",
          display_order: 1,
          latitude: 34.3407,
          longitude: 134.0481,
          areas: []
        },
        {
          prefecture_id: 39,
          prefecture_name: "山口県",
          display_order: 1,
          latitude: 34.1854,
          longitude: 131.4701,
          areas: []
        }
      ]
    },
    {
      region_id: 2,
      region_name: "東北",
      display_order: 2,
      prefectures: [
        {
          prefecture_id: 2,
          prefecture_name: "青森県",
          display_order: 1,
          latitude: 40.8244,
          longitude: 140.74,
          areas: [
            {
              area_id: 4,
              area_name: "青森市",
              display_order: 1,
              latitude: 40.8244,
              longitude: 140.74,
              bus_stops: [
                {
                  bus_stop_id: 5,
                  bus_stop_name: "青森駅前",
                  bus_stop_short_name: "青森駅前",
                  latitude: 40.8283,
                  longitude: 140.7394,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            }
          ]
        },
        {
          prefecture_id: 3,
          prefecture_name: "岩手県",
          display_order: 2,
          latitude: 39.7036,
          longitude: 141.1527,
          areas: [
            {
              area_id: 5,
              area_name: "盛岡市",
              display_order: 1,
              latitude: 39.7036,
              longitude: 141.1527,
              bus_stops: [
                {
                  bus_stop_id: 6,
                  bus_stop_name: "盛岡駅中央口",
                  bus_stop_short_name: "盛岡中央口",
                  latitude: 39.7017,
                  longitude: 141.1370,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ["map_device"]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      region_id: 3,
      region_name: "中部",
      display_order: 3,
      prefectures: [
        {
          prefecture_id: 23,
          prefecture_name: "愛知県",
          display_order: 1,
          latitude: 35.1802,
          longitude: 136.9066,
          areas: [
            {
              area_id: 6,
              area_name: "名古屋市",
              display_order: 1,
              latitude: 35.1815,
              longitude: 136.9066,
              bus_stops: [
                {
                  bus_stop_id: 7,
                  bus_stop_name: "名古屋駅",
                  bus_stop_short_name: "名古屋駅",
                  latitude: 35.1706,
                  longitude: 136.8816,
                  has_toilet: true,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      region_id: 4,
      region_name: "西部",
      display_order: 3
    },
    {
      region_id: 5,
      region_name: "西部",
      display_order: 3
    },
    {
      region_id: 6,
      region_name: "西部",
      display_order: 3
    },
    {
      region_id: 7,
      region_name: "西部",
      display_order: 3
    }
  ]
};


const regionData = [
  {
    region_id: 1,
    region_name: "予約履歴から",
    display_order: 1,
    prefectures: [
      {
        prefecture_id: 1,
        prefecture_name: "北海道",
        display_order: 1,
        latitude: 43.0642,
        longitude: 141.3469,
        areas: []
      }
    ]
  },
  {
    region_id: 2,
    region_name: "お気に入り停留所",
    display_order: 2,
    prefectures: [
      {
        prefecture_id: 2,
        prefecture_name: "青森県",
        display_order: 1,
        latitude: 40.8244,
        longitude: 140.74,
        areas: [
          {
            area_id: 3,
            area_name: "青森市",
            display_order: 1,
            latitude: 40.8244,
            longitude: 140.74,
          }
        ]
      },
      {
        prefecture_id: 3,
        prefecture_name: "秋田県",
        display_order: 2,
        latitude: 39.7181,
        longitude: 140.103,
        areas: [
          {
            area_id: 4,
            area_name: "秋田市",
            display_order: 1,
            latitude: 39.7181,
            longitude: 140.103,
          }
        ]
      }
    ]
  }
];

const dataList: DataItem[] = [
  { id: 1, name: '东京', type: '东京都' },
  { id: 2, name: '大阪', type: '大阪府' },
  { id: 3, name: '京都', type: '京都府' },
  { id: 4, name: '大津', type: '滋贺县' },
  { id: 5, name: '大牟田', type: '福冈县' },
  { id: 6, name: '大垣', type: '岐阜县' },
  { id: 7, name: '大船渡', type: '岩手县' },
  { id: 8, name: '大凑', type: '青森县' },
  { id: 9, name: '大洗', type: '茨城县' },
  { id: 10, name: '大宫', type: '埼玉县' },
  { id: 11, name: '大府', type: '爱知县' },
  { id: 12, name: '大村', type: '长崎县' },
  { id: 13, name: '大津', type: '滋贺县' },
  { id: 14, name: '大崎', type: '宫城县' },
  { id: 15, name: '大町', type: '长野县' },
  { id: 16, name: '大正', type: '大阪府' },
  { id: 17, name: '大川', type: '福井县' },
  { id: 18, name: '大东', type: '冲绳县' },
  { id: 19, name: '大里', type: '千叶县' },
  { id: 20, name: '大平', type: '奈良县' },
  { id: 21, name: '大野', type: '富山县' },
  { id: 22, name: '大森', type: '东京都' },
  { id: 52, name: '大岛over', type: '东京都' },
];

const searchValue = ref('');
const searchResults = ref<DataItem[]>([]);

const handleSearch = () => {
  if (searchValue.value.trim() === '') {
    searchResults.value = [];
    return;
  }
  searchResults.value = dataList.filter(item =>
    item.name.includes(searchValue.value)
  );
};

// 选中的标签
const activeTag = ref<string>('');
const expandedRegions = ref<Set<string>>(new Set());
const selectedRegions = ref<Set<string>>(new Set());

// 从JSON数据中提取地区标签
const regionTags = computed(() => {
  return jsonData.data.map(region => region.region_name);
});

// 根据选中的标签获取当前地区数据
const currentRegionData = computed(() => {
  return jsonData.data.find(region => region.region_name === activeTag.value);
});

// 设置选中的标签
function setActiveTag(tag: string): void {
  activeTag.value = tag;
  expandedRegions.value.clear(); // 切换标签时折叠所有地区
}

// 切换地区展开/折叠状态
function toggleRegion(regionId: string): void {
  if (expandedRegions.value.has(regionId)) {
    expandedRegions.value.delete(regionId);
  } else {
    expandedRegions.value.add(regionId);
  }
}

// 获取所有子项的ID - 扩展为可以处理不同层级
function getAllChildIds(item: Prefecture | Area): string[] {
  const childIds: string[] = [];

  if ('areas' in item) {
    // 处理都道府县层级
    item.areas.forEach(area => {
      childIds.push(`area_${area.area_id}`);

      // 添加所有巴士站ID
      area.bus_stops.forEach(busStop => {
        childIds.push(`bus_${busStop.bus_stop_id}`);
      });
    });
  } else if ('bus_stops' in item) {
    // 处理区域层级
    item.bus_stops.forEach(busStop => {
      childIds.push(`bus_${busStop.bus_stop_id}`);
    });
  }

  return childIds;
}

// 检查是否所有子项都被选中
function areAllChildrenSelected(item: Prefecture | Area): boolean {
  const childIds = getAllChildIds(item);
  return childIds.length > 0 && childIds.every(id => selectedRegions.value.has(id));
}

// 检查是否有部分子项被选中
function areSomeChildrenSelected(item: Prefecture | Area): boolean {
  const childIds = getAllChildIds(item);
  return childIds.some(id => selectedRegions.value.has(id)) && !areAllChildrenSelected(item);
}

// 切换选择状态（带级联功能）
function toggleSelection(id: string, item?: Prefecture | Area, event?: Event): void {
  if (item) {
    // 如果是父级选择框
    const allChildIds = getAllChildIds(item);
    const isCurrentlySelected = areAllChildrenSelected(item);

    if (isCurrentlySelected) {
      // 如果当前全选，则取消全选
      allChildIds.forEach(childId => selectedRegions.value.delete(childId));
      selectedRegions.value.delete(id);
    } else {
      // 如果当前未全选，则全选
      allChildIds.forEach(childId => selectedRegions.value.add(childId));
      selectedRegions.value.add(id);
    }
  } else {
    // 如果是子项选择框
    const target = event?.target as HTMLInputElement;
    if (target?.checked) {
      selectedRegions.value.add(id);
    } else {
      selectedRegions.value.delete(id);
    }
  }
}

// 检查是否被选中
function isSelected(id: string, item?: Prefecture | Area): boolean {
  if (item) {
    // 父级选择框：检查是否所有子项都被选中
    return areAllChildrenSelected(item);
  }
  return selectedRegions.value.has(id);
}

// 检查是否为不确定状态（部分选中）
function isIndeterminate(item: Prefecture | Area): boolean {
  return areSomeChildrenSelected(item);
}

// 返回按钮处理函数
function handleBack() {
  window.history.go(-1);
}

// 获取标签显示文本
function getTagText(labels: string[]): string {
  return labels.join(', ');
}

// 初始化
onMounted(() => {
  // 设置默认选中第一个标签
  if (regionTags.value.length > 0) {
    activeTag.value = regionTags.value[0];
  }
});
</script>

<template>
  <div class="regionSelector">
    <!-- 头部区域切换 -->
    <div class="regionSelectorHeader">
      <!-- 返回按钮和标题 -->
      <div class="regionSelectorHeaderTop">
        <button class="regionSelectorBackBtn" @click="handleBack">
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M10 4L6 8l4 4" stroke="#26499D" stroke-width="1.5" fill="none" />
          </svg>
        </button>
        <h3 class="regionSelectorTitle">到着地</h3>
      </div>

      <!-- 搜索框 -->
      <div>
        <v-text-field prepend-inner-icon="mdi-magnify" color="grey" v-model="searchValue" placeholder="到着地を入力"
          @input="handleSearch">
        </v-text-field>

        <SearchResult :searchResults="searchResults" />
      </div>

      <!-- 已登录的状态 使用regionData数组 -->
       <!-- 已登录的状态 使用regionData数组（核心修改区域） -->
      <div v-if="isLoggedIn" class="regionSelectorList loggedInRegionList">
        <!-- 一级：地区层级（必然有下一级，保留折叠图标） -->
        <div v-for="region in regionData" :key="region.region_id" class="regionSelectorItems">
          <div class="regionSelectorItemHeader" @click="toggleRegion(`loggedIn_region_${region.region_id}`)">
            <label class="regionSelectorCheckbox">
              <input type="checkbox" :checked="isSelected(`loggedIn_region_${region.region_id}`)" />
              <span class="regionSelectorLabels">{{ region.region_name }}</span>
            </label>
            <!-- 地区层级必有下一级（都道府县），保留折叠图标 -->
            <button
              :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`loggedIn_region_${region.region_id}`) }]"
              @click.stop="toggleRegion(`loggedIn_region_${region.region_id}`)">
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
              </svg>
            </button>
          </div>

          <!-- 二级：都道府县层级（判断是否有下一级，决定是否显示折叠图标） -->
          <div v-if="expandedRegions.has(`loggedIn_region_${region.region_id}`)" class="regionSelectorChildren">
            <div v-for="prefecture in region.prefectures" :key="prefecture.prefecture_id"
              class="regionSelectorChildItem">
              <div class="regionSelectorChildHeader" 
                @click="toggleRegion(`loggedIn_prefecture_${prefecture.prefecture_id}`)">
                <label class="regionSelectorCheckbox">
                  <input type="checkbox" :checked="isSelected(`loggedIn_prefecture_${prefecture.prefecture_id}`)" />
                  <span class="regionSelectorLabels">{{ prefecture.prefecture_name }}</span>
                </label>
                <button
                  v-if="prefecture.areas && prefecture.areas.length > 0" 
                  :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`loggedIn_prefecture_${prefecture.prefecture_id}`) }]"
                  @click.stop="toggleRegion(`loggedIn_prefecture_${prefecture.prefecture_id}`)">
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
                  </svg>
                </button>
                <div v-else  style="padding: 16px 16px 16px 32px;"></div>
              </div>

              <!-- 三级：区域层级（regionData中无下一级，直接移除折叠图标） -->
              <div v-if="expandedRegions.has(`loggedIn_prefecture_${prefecture.prefecture_id}`)"
                class="regionSelectorSubChildren">
                <div v-for="area in prefecture.areas" :key="area.area_id" class="regionSelectorSubChildItem">
                  <label class="regionSelectorCheckbox">
                    <input type="checkbox" :checked="isSelected(`loggedIn_area_${area.area_id}`)" />
                    <span class="regionSelectorLabels">{{ area.area_name }}</span>
                  </label>
                  <!-- 区域层级无下一级，不显示折叠图标 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 地区标签 -->
      <div class="regionSelectorTags">
        <button v-for="tag in regionTags" :key="tag"
          :class="['regionSelectorTag', { 'regionSelectorTagActive': activeTag === tag }]" @click="setActiveTag(tag)">
          {{ tag }}
        </button>
      </div>
    </div>

    <!-- 地区列表 -->
    <div class="regionSelectorContent">
      <div class="regionSelectorList" v-if="currentRegionData">
        <!-- 都道府县层级 -->
        <div v-for="prefecture in currentRegionData.prefectures" :key="prefecture.prefecture_id"
          class="regionSelectorItem"
          :class="{ 'regionSelectorItemSelected': isSelected(`prefecture_${prefecture.prefecture_id}`, prefecture) }">
          <div class="regionSelectorItemHeader" @click="toggleRegion(`prefecture_${prefecture.prefecture_id}`)">
            <label class="regionSelectorCheckbox">
              <input type="checkbox" :checked="isSelected(`prefecture_${prefecture.prefecture_id}`, prefecture)"
                :indeterminate="isIndeterminate(prefecture)" @click.stop
                @change="toggleSelection(`prefecture_${prefecture.prefecture_id}`, prefecture, $event)" />
              <span class="regionSelectorCheckmark"></span>
              <span class="regionSelectorLabel">{{ prefecture.prefecture_name }}</span>
            </label>
            <button
              :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`prefecture_${prefecture.prefecture_id}`) }]"
              @click.stop="toggleRegion(`prefecture_${prefecture.prefecture_id}`)">
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
              </svg>
            </button>
          </div>

          <!-- 区域层级 -->
          <div v-if="expandedRegions.has(`prefecture_${prefecture.prefecture_id}`)" class="regionSelectorChildren">
            <div v-for="area in prefecture.areas" :key="area.area_id" class="regionSelectorChildItem"
              :class="{ 'regionSelectorChildItemSelected': isSelected(`area_${area.area_id}`, area) }">
              <div class="regionSelectorChildHeader" @click="toggleRegion(`area_${area.area_id}`)">
                <label class="regionSelectorCheckbox">
                  <input type="checkbox" :checked="isSelected(`area_${area.area_id}`, area)"
                    :indeterminate="isIndeterminate(area)" @click.stop
                    @change="toggleSelection(`area_${area.area_id}`, area, $event)" />
                  <span class="regionSelectorCheckmark"></span>
                  <span class="regionSelectorLabel">{{ area.area_name }}</span>
                </label>
                <button v-if="area.bus_stops && area.bus_stops.length > 0"
                  :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`area_${area.area_id}`) }]"
                  @click.stop="toggleRegion(`area_${area.area_id}`)">
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
                  </svg>
                </button>
              </div>

              <!-- 巴士站层级 -->
              <div v-if="expandedRegions.has(`area_${area.area_id}`) && area.bus_stops"
                class="regionSelectorSubChildren">
                <div v-for="busStop in area.bus_stops" :key="busStop.bus_stop_id" class="regionSelectorSubChildItem"
                  :class="{ 'regionSelectorSubChildItemSelected': isSelected(`bus_${busStop.bus_stop_id}`) }">
                  <label class="regionSelectorCheckbox">
                    <input type="checkbox" :checked="isSelected(`bus_${busStop.bus_stop_id}`)"
                      @change="toggleSelection(`bus_${busStop.bus_stop_id}`, undefined, $event)" />
                    <span class="regionSelectorCheckmark"></span>
                    <span class="regionSelectorLabel">{{ busStop.bus_stop_name }}</span>
                  </label>
                  <span v-if="busStop.labels && busStop.labels.length > 0" class="regionSelectorTagLabel">
                    {{ getTagText(busStop.labels) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="regionSelectorFooter">
      <button class="regionSelectorConfirmBtn" :disabled="selectedRegions.size === 0">
        {{ selectedRegions.size > 0 ? `決定` : '決定' }}
      </button>
    </div>
  </div>
</template>


<style lang="scss">

.regionSelectorLabels {
  color: #26499D;
}

.regionSelectorItems {
  border-top: 1px solid #DFDFDF;
  transition: background-color 0.2s ease;
}

.regionSelectorItems:last-child {
  margin-bottom: 20px;
}

.regionSelectorItems:last-of-type {
  border-bottom: 1px solid #DFDFDF;
}

.regionSelectorItem {
  border-top: 1px solid #DFDFDF;
  margin: 0 20px;
  transition: background-color 0.2s ease;
}

.regionSelectorItem:last-child {
  margin-bottom: 60px;
}

.regionSelectorList>.regionSelectorItem:last-of-type {
  border-bottom: 1px solid #DFDFDF;
}

.regionSelector {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;


  &Header {
    padding: 12px 16px;
  }

  &HeaderTop {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  &BackBtn {
    padding: 4px;
    border: none;
    background: transparent;
    color: #666;
    margin-right: 8px;
  }

  &Title {
    font-size: 16px;
    font-weight: 500;
    color: #26499D;
    margin: 0;
    flex: 1;
    text-align: center;
  }

  &SearchBox {
    margin-bottom: 12px;
  }

  &SearchInput {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #4096ff;
    }

    &::placeholder {
      color: #999;
    }
  }


  &Tags {
    text-align: left;
    overflow: hidden;
  }

  &Tag {
    height: 32px;
    border-radius: 6px;
    font-size: 13px;
    color: #000;
    border: 1px solid #9CBCD4;
    width: calc(25% - 10px);
    min-width: 50px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin: 5px;

    &Active {
      background: #9CBCD4;
      color: #000;
    }
  }


  &Content {
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    margin-bottom: 80px;
  }

  &List {
    padding: 0;
  }

  &ItemHeader,
  &ChildHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    transition: background-color 0.2s ease;
  }

  &Checkbox {
    display: flex;
    align-items: center;
    flex: 1;

    input[type="checkbox"] {
      display: none;
    }
  }

  &Checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #26499D;
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    transition: all 0.2s ease;

    input:checked+& {
      background: #26499D;
      border-color: #26499D;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -60%) rotate(45deg);
        width: 4px;
        height: 8px;
        border: 2px solid #ffffff;
        border-top: none;
        border-left: none;
      }
    }

    // 不确定状态样式
    input:indeterminate+& {
      background: #26499D;
      border-color: #26499D;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 2px;
        background: #ffffff;
        border: none;
      }
    }
  }

  &Label {
    font-size: 14px;
    color: #333;
    user-select: none;
  }

  &Toggle {
    padding: 4px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: #26499D;
    transition: all 0.2s ease;

    &Expanded {
      transform: rotate(90deg);
    }
  }

  &Children {
    border-top: 1px solid #f0f0f0;
  }

  &ChildItem {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #E7F2FA;
    }
  }

  &ChildHeader {
    padding: 8px 16px 8px 32px;
  }

  &SubChildren {
    border-top: 1px solid #e0e0e0;
  }

  &SubChildItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px 12px 48px;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #E7F2FA;
    }
  }

  &TagLabel {
    font-size: 12px;
    color: #000;
    border: 1px solid #9CBCD4;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
  }

  &Footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  &ConfirmBtn {
    width: 100%;
    padding: 12px 24px;
    background: #26499D;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:disabled {
      background: #DFDFDF;
      color: #7D7D7D;
      cursor: not-allowed;
    }

  }
}
</style>
