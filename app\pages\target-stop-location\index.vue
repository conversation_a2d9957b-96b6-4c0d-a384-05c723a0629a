<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { onMounted, ref, watch } from 'vue'
import {
    useSpotStore
} from '~/stores/edit';
import type { TouristSpot } from '~/types/edit';
const spotStore = useSpotStore();


definePageMeta({
    hideFooter: true,
    footerPlaceholderHeight: 86
})

const route = useRoute()
const router = useRouter()

// 地点类型
interface LocationItem {
    id: number
    name: string
}

// 初始地点数据
const initialLocations: LocationItem[] = [
    { id: 1, name: '東武日光駅' },
    { id: 2, name: '清瀬IC' },
    { id: 3, name: '大宮駅' },
    { id: 4, name: '宇都宮駅' },
    { id: 5, name: '高崎駅' },
    { id: 6, name: '熊谷駅' },
    { id: 7, name: '小山駅' },
    { id: 8, name: '栃木駅' },
    { id: 9, name: '足利駅' },
    { id: 10, name: '太田駅' },
    { id: 11, name: '前橋駅' },
    { id: 12, name: '伊勢崎駅' },
    { id: 13, name: '佐野駅' },
    { id: 14, name: '鹿沼駅' },
    { id: 15, name: '那須塩原駅' },
    { id: 16, name: '黒磯駅' },
    { id: 17, name: '白河市駅' },
    { id: 18, name: '会津若松駅' },
    { id: 19, name: '新潟駅' },
    { id: 20, name: '長岡駅' },
    { id: 21, name: '富山駅' },
    { id: 22, name: '金沢駅' },
    { id: 23, name: '福井駅' },
    { id: 24, name: '敦賀駅' },
    { id: 25, name: '京都駅' },
    { id: 26, name: '大阪駅' },
    { id: 27, name: '神戸駅' },
    { id: 28, name: '姫路駅' },
    { id: 29, name: '岡山駅' },
    { id: 30, name: '広島駅' },
    { id: 31, name: '下関駅' },
    { id: 32, name: '福岡駅' },
    { id: 33, name: '熊本駅' },
    { id: 34, name: '鹿児島駅' },
    { id: 35, name: '札幌駅' },
    { id: 36, name: '旭川駅' },
    { id: 37, name: '函館駅' },
    { id: 38, name: '仙台駅' },
    { id: 39, name: '秋田駅' },
    { id: 40, name: '青森駅11' }
]

const locations = ref<LocationItem[]>(initialLocations)

// 当前选中的地点
const selectedId = ref<number | null>(null)

function handleBack() {
    window.history.go(-1)
}

// api
async function fetchMoreLocations(): Promise<LocationItem[]> {
    // 这里可以添加实际的API调用
    return []
}

async function load({ done }: { done: (status: string) => void }) {
    try {
        const newLocations = await fetchMoreLocations()
        locations.value.push(...newLocations)
        done('ok')
    } catch (error) {
        console.error('加载失败:', error)
        done('error')
    }
}


function contentSearch() {
    // 查找选中的地点
    const selectedLocation = locations.value.find(l => l.id === selectedId.value)

    if (selectedLocation) {
        spotStore.setCurrentTouristData(selectedLocation as unknown as TouristSpot);

        console.log('完整信息:', selectedLocation)
        router.push({
            path: '/tourist-facilities/routeSelect',
        });
    }
}

watch(
    () => route.params,
    (newParams) => {
        console.log('路由参数变化:', newParams)
    },
    { immediate: true }
)

onMounted(() => {

})
</script>
<template>
    <div>
        <!-- 标题栏 -->
        <div class="regionHeaderTop createPad">
            <button class="regionSelectorBackBtn" @click="handleBack">
                <svg width="16" height="16" viewBox="0 0 16 16">
                    <path d="M10 4L6 8l4 4" stroke="#26499D" stroke-width="1.5" fill="none" />
                </svg>
            </button>
            <h3 class="regionSelectorTitle">到着地</h3>
        </div>

        <!-- 无限滚动 + 单选框 -->
        <div class="scroll-container">
            <v-radio-group v-model="selectedId" class="square-radio-group" color="#123d65"
                true-icon="mdi-checkbox-marked" false-icon="mdi-checkbox-blank-outline">
                <v-infinite-scroll :height="900" :items="locations" @load="load" :finished="false" :loading="false">
                    <template v-for="location in locations" :key="location.id">
                        <div class="radio-item" :class="{ active: selectedId === location.id }">
                            <v-radio :label="location.name" :value="location.id" color="#123d65"></v-radio>
                        </div>
                    </template>
                </v-infinite-scroll>
            </v-radio-group>
        </div>

        <!-- 底部按钮 -->
        <div class="fixedBottomContainer" @click="contentSearch">
            <div class="content">
                決定
            </div>
        </div>
    </div>
</template>


<style scoped lang="scss">
.scroll-container {
    padding: 16px;
}

.square-radio-group .v-selection-control {
    border-top: 1px solid #ccc;
    padding: 8px;
}


.square-radio-group .v-selection-control:last-child {
    border-bottom: 1px solid #ccc;
}

.radio-item.active {
    background-color: #e6f0ff; // 高亮背景色
}

.fixedBottomContainer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    height: 86px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.2);
}

.content {
    text-align: center;
    font-size: 16px;
    color: #fff;
    background-color: #26499D;
    width: 343px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
    cursor: pointer;
}
</style>
