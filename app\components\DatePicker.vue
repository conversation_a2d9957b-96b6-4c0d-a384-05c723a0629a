<!-- DatePicker.vue -->
<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch
} from 'vue'

interface DateInfo {
  day: number
  date: Date
  isCurrentMonth: boolean
  isDisabled: boolean
  isSelected: boolean
  isToday: boolean
  key: string
}

// Props
interface Props {
  minDate?: Date
  selectedDateValue?: string  // 接收当前选中的日期字符串
}

const props = withDefaults(defineProps<Props>(), {
  minDate: () => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return today
  },
  selectedDateValue: ''
})

// Emits
const emit = defineEmits<{
  'dateSelected': [value: string]
  'close': []
}>()

// Reactive data
const selectedDate = ref<Date | null>(null)
const currentYear = ref(2025)
const currentMonth = ref(3) // March
const today = new Date()
today.setHours(0, 0, 0, 0)

// 解析日期字符串为Date对象
const parseSelectedDate = (dateString: string): Date | null => {
  if (!dateString) return null

  // 支持多种日期格式: YYYY-M-D, YYYY-MM-DD
  const parts = dateString.split('-')
  if (parts.length === 3) {
    const year = parseInt(parts[0])
    const month = parseInt(parts[1]) - 1 // JavaScript月份从0开始
    const day = parseInt(parts[2])

    if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
      const date = new Date(year, month, day)
      date.setHours(0, 0, 0, 0)
      return date
    }
  }
  return null
}

// 初始化选中的日期
const initializeSelectedDate = () => {
  if (props.selectedDateValue) {
    const parsedDate = parseSelectedDate(props.selectedDateValue)
    if (parsedDate) {
      selectedDate.value = parsedDate
      // 将日历显示到选中日期的月份
      currentYear.value = parsedDate.getFullYear()
      currentMonth.value = parsedDate.getMonth() + 1
    }
  }
}

// Computed
const calendarDates = computed(() => {
  const dates: DateInfo[] = []
  const firstDayOfMonth = new Date(currentYear.value, currentMonth.value - 1, 1)
  const startDate = new Date(firstDayOfMonth)

  // Get the first Sunday of the calendar (might be from previous month)
  startDate.setDate(startDate.getDate() - startDate.getDay())

  // Generate 42 dates (6 weeks * 7 days)
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    date.setHours(0, 0, 0, 0)

    const isCurrentMonth = date.getMonth() === currentMonth.value - 1
    // 只有过去的日期（不包括今天）才被禁用，未来的日期都可以选择
    const isDisabled = date < props.minDate
    const isSelected = selectedDate.value &&
        date.getFullYear() === selectedDate.value.getFullYear() &&
        date.getMonth() === selectedDate.value.getMonth() &&
        date.getDate() === selectedDate.value.getDate()
    const isToday = date.getTime() === today.getTime()

    dates.push({
      day: date.getDate(),
      date: new Date(date),
      isCurrentMonth,
      isDisabled,
      isSelected,
      isToday,
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
    })
  }

  return dates
})

// Methods
const selectDate = (dateInfo: DateInfo) => {
  if (dateInfo.isDisabled) return

  selectedDate.value = dateInfo.date
  // 格式化日期为 YYYY-M-D 格式
  const year = dateInfo.date.getFullYear()
  const month = dateInfo.date.getMonth() + 1
  const day = dateInfo.date.getDate()
  const dateString = `${year}-${month}-${day}`

  // 发送选中的日期字符串给父组件
  emit('dateSelected', dateString)
  // 选择日期后关闭组件
  emit('close')
}

const previousMonth = () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12
    currentYear.value--
  } else {
    currentMonth.value--
  }
}

const nextMonth = () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1
    currentYear.value++
  } else {
    currentMonth.value++
  }
}

// 监听props变化，更新选中日期
watch(
    () => props.selectedDateValue,
    (newValue) => {
      if (newValue) {
        const parsedDate = parseSelectedDate(newValue)
        if (parsedDate) {
          selectedDate.value = parsedDate
        }
      } else {
        selectedDate.value = null
      }
    },
    { immediate: true }
)

onMounted(() => {
  // 首先尝试从props初始化选中日期和显示月份
  initializeSelectedDate()

  // 如果没有选中日期，则初始化当前年月为今天的年月
  if (!props.selectedDateValue) {
    const now = new Date()
    currentYear.value = now.getFullYear()
    currentMonth.value = now.getMonth() + 1
  }
})
</script>

<template>
  <div class="calendar-container">
    <v-card class="calendar-card" elevation="2">
      <div
          class="close-btn"
          @click="$emit('close')"
      >
        <v-icon>mdi-close</v-icon>
      </div>

      <!-- Calendar header -->
      <div class="calendar-header">
        <v-btn size="small" variant="outlined" class="nav-btn" @click="previousMonth">
          <v-icon left>mdi-chevron-left</v-icon>
          前月
        </v-btn>

        <div class="month-year-display">
          <span class="year-text">{{ currentYear }}年</span>
          <span class="month-text">{{ String(currentMonth).padStart(2, '0') }}月</span>
        </div>

        <v-btn variant="outlined" class="nav-btn" @click="nextMonth">
          翌月
          <v-icon right>mdi-chevron-right</v-icon>
        </v-btn>
      </div>

      <!-- Calendar grid -->
      <div class="calendar-grid">
        <!-- Day headers -->
        <div class="day-headers">
          <div class="day-header sunday">日</div>
          <div class="day-header weekday">月</div>
          <div class="day-header weekday">火</div>
          <div class="day-header weekday">水</div>
          <div class="day-header weekday">木</div>
          <div class="day-header weekday">金</div>
          <div class="day-header saturday">土</div>
        </div>

        <!-- Calendar dates -->
        <div class="calendar-dates">
          <div v-for="date in calendarDates" :key="date.key" :class="[
        'date-cell',
        {
          'other-month': !date.isCurrentMonth,
          'disabled': date.isDisabled,
          'selected': date.isSelected,
          'today': date.isToday
        }
      ]"
               @click="selectDate(date)">
            <div class="date-inner">
              {{ date.day }}
            </div>
          </div>
        </div>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
.calendar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.calendar-card {
  width: 341px;
  background: #ffffff;
  border-radius: 10px;
  padding: 24px 16px;
  position: relative;
}

.close-btn {
  text-align: end;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 24px 0;
}

.nav-btn {
  border-color: #9cbcd4 !important;
  color: #000000 !important;
  font-size: 15px;
  text-transform: none;
  height: 48px !important;
  border-radius: 4px !important;
}

.month-year-display {
  display: flex;
  align-items: center;
  gap: 6px;
}

.year-text {
  color: #000000;

  font-size: 12px;
  line-height: 20px;
}

.month-text {
  color: #000000;

  font-size: 20px;
  font-weight: 400;
}

.calendar-grid {
  border: 1px solid #b5b5b5;
  width: 309px;
}

.day-headers {
  display: flex;
  height: 20px;
}

.day-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #b5b5b5;
  border-top: 1px solid #b5b5b5;
  font-size: 12px;
  font-weight: 700;
  color: #ffffff;
  padding: 4px 0;
}

.day-header:first-child {
  border-left: 1px solid #b5b5b5;
}

.day-header.sunday {
  background: #ed785f;
}

.day-header.weekday {
  background: #7d7d7d;
}

.day-header.saturday {
  background: #9cbcd4;
}

.calendar-dates {
  display: flex;
  flex-wrap: wrap;
  height: 288px;
}

.date-cell {
  width: calc(100% / 7);
  height: 48px;
  border-right: 1px solid #b5b5b5;
  border-top: 1px solid #b5b5b5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
}

.date-cell:nth-child(7n+1) {
  border-left: 1px solid #b5b5b5;
}

.date-cell.other-month:not(.disabled):hover {
  background: #d0d0d0;
}

.date-cell.disabled {
  background: #dfdfdf;
  cursor: not-allowed;
}

.date-cell.disabled .date-inner {
  color: #7d7d7d;
}

.date-cell:not(.disabled):not(.other-month):hover {
  background: #f0f8ff;
}

.date-cell.selected .date-inner {
  background: #26499d;
  color: #ffffff;
  border-radius: 18px;
  border: 2px solid #26499d;
}

.date-cell.today .date-inner {
  border-radius: 18px;
  border: 2px solid #26499d;

}

.date-inner {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333333;
  font-size: 14px;
  line-height: 22px;
  font-weight: 700;
  border-radius: 18px;
  transition: all 0.2s ease;
}

.other-month .date-inner {
  color: #7d7d7d;
}
</style>