import LocalCache, { StorageType } from './cache'

const TokenKey = 'ConemobiToken'
const TokenPrefix = 'Bearer '

const UserName = 'un'
const CompanyName = 'cn'
const UserRole = 'r'

const isLogin = () => {
  return !!LocalCache.getCache(TokenKey)
}
const getToken = () => {
  return LocalCache.getCache(TokenKey)
}
const setToken = (token: string) => {
  LocalCache.setCache(TokenKey, token)
}

const clearToken = () => {
  LocalCache.deleteCache(TokenKey)
}

const getUserInfo = (key?: string) => {
  switch (key) {
    case 'name':
      return LocalCache.getCache(UserName)
    case 'company':
      return LocalCache.getCache(CompanyName)
    case 'role':
      return LocalCache.getCache(UserRole)
  }

  return {
    username: LocalCache.getCache(UserName),
    companyname: LocalCache.getCache(CompanyName),
    role: LocalCache.getCache(UserRole)
  }
}
const setUserInfo = (username: string, companyname: string, role: string) => {
  LocalCache.setCache(UserName, username)
  LocalCache.setCache(CompanyName, companyname)
  LocalCache.setCache(UserRole, role)
}
const clearUserInfo = () => {
  LocalCache.deleteCache(UserName)
  LocalCache.deleteCache(CompanyName)
  LocalCache.deleteCache(UserRole)
}

export {
  TokenPrefix,
  isLogin,
  getToken,
  setToken,
  clearToken,
  getUserInfo,
  setUserInfo,
  clearUserInfo
}
