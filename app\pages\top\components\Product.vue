<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import preparingImage from '~/assets/image/preparingImage.png'

// TypeScript 接口定义
interface Product {
  planProductCode: string;
  planProductName: string;
  description: string;
  image?: string;
  isMock?: boolean;
}

const router = useRouter();

// 响应式数据
const products = ref<Product[]>([]);
const loading = ref<boolean>(false);
const error = ref<string | null>(null);

// Mock 数据填充 - 更新为更合适的占位文字
const mockData: Product[] = [
  {
    planProductCode: 'mock-1',
    planProductName: '準備中',
    description: 'お楽しみにお待ちください',
    isMock: true
  },
  {
    planProductCode: 'mock-2',
    planProductName: '準備中',
    description: 'お楽しみにお待ちください',
    isMock: true
  },
  {
    planProductCode: 'mock-3',
    planProductName: '準備中',
    description: 'お楽しみにお待ちください',
    isMock: true
  },
  {
    planProductCode: 'mock-4',
    planProductName: '準備中',
    description: 'お楽しみにお待ちください',
    isMock: true
  },
  {
    planProductCode: 'mock-5',
    planProductName: '準備中',
    description: 'お楽しみにお待ちください',
    isMock: true
  }
];

// 确保总是有5个产品用于显示
const displayProducts = computed<Product[]>(() => {
  const result = [...products.value];
  while (result.length < 5) {
    const mockIndex = result.length;
    result.push(mockData[mockIndex]);
  }
  return result.slice(0, 5);
});

// 获取产品数据
const fetchProducts = async (): Promise<void> => {
  loading.value = true;
  error.value = null;

  // 临时使用 Mock 数据
  products.value = [
    { planProductCode: 'mock-1', planProductName: 'おすすめプラン', description: '詳細はこちらをご確認ください',image:`https://picsum.photos/300/200?random=mock-1` },
    { planProductCode: 'mock-2', planProductName: '特別企画', description: '詳細はこちらをご確認ください',image:`https://picsum.photos/300/200?random=mock-2`},
    { planProductCode: 'mock-3', planProductName: '限定ツアー', description: '詳細はこちらをご確認ください',image:`https://picsum.photos/300/200?random=mock-3`},
    { planProductCode: 'mock-4', planProductName: '人気コース', description: '詳細はこちらをご確認ください' ,image:`https://picsum.photos/300/200?random=mock-4`},
  ];

  try {
    // 实际 API 调用（目前注释掉）
    // const response = await $fetch('/api/backend/plan-products', {
    //   query: { limit: 5 }
    // });
    //
    // if (response?.data?.planProducts) {
    //   products.value = response.data.planProducts;
    // } else {
    //   throw new Error('Invalid response format');
    // }
  } catch (err) {
    console.error('Failed to fetch products:', err);
    error.value = err instanceof Error ? err.message : 'Unknown error occurred';
    products.value = [];
  } finally {
    loading.value = false;
  }
};

// 跳转到详情页
const navigateToDetail = (product: Product | undefined): void => {
  if (!product || product.isMock) return;
  router.push(`/product/detail/${product.planProductCode}`);
};

// 跳转到列表页
const navigateToProductList = (): void => {
  router.push('/products');
};

// 获取图片
const getImage = (product: Product | undefined): string => {
  if (product?.isMock) return preparingImage;

  if (!product) return 'https://picsum.photos/300/200?random=default';
  if (product.image) return product.image;
  return `https://picsum.photos/300/200?random=${product.planProductCode}`;
};

// 初始化
onMounted(() => {
  fetchProducts();
});
</script>
<template>
  <div class="product-wrapper">
    <h2 class="section-title">お得な商品</h2>
    <div class="title-underline"></div>

    <!-- Loading State -->
    <div v-if="loading" class="loading">読み込み中...</div>

    <!-- Error State -->
    <div v-else-if="error" class="error">
      <p>データの読み込みに失敗しました</p>
      <button @click="fetchProducts">再試行</button>
    </div>

    <!-- Content -->
    <div v-else>
      <!-- PC Layout -->
      <div class="desktop-layout d-none d-md-block">
        <v-container fluid>
          <!-- 第一行 -->
          <v-row no-gutters class="mb-4">
            <!-- 产品1：左边图片，右边文字 -->
            <v-col cols="7" class="pr-2">
              <div class="product-card product-1" @click="navigateToDetail(displayProducts[0])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[0])" :alt="displayProducts[0]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[0]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[0]?.description }}</p>
                </div>
              </div>
            </v-col>

            <!-- 产品2：上边图片，下边文字 -->
            <v-col cols="5" class="pl-2">
              <div class="product-card product-2" @click="navigateToDetail(displayProducts[1])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[1])" :alt="displayProducts[1]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[1]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[1]?.description }}</p>
                </div>
              </div>
            </v-col>
          </v-row>

          <!-- 第二行 -->
          <v-row no-gutters>
            <!-- 产品3：上边图片，下边文字 -->
            <v-col cols="2" class="pr-2">
              <div class="product-card product-3" @click="navigateToDetail(displayProducts[2])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[2])" :alt="displayProducts[2]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[2]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[2]?.description }}</p>
                </div>
              </div>
            </v-col>

            <!-- 产品4：左边图片，右边文字 -->
            <v-col cols="6" class="px-1">
              <div class="product-card product-4" @click="navigateToDetail(displayProducts[3])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[3])" :alt="displayProducts[3]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[3]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[3]?.description }}</p>
                </div>
              </div>
            </v-col>

            <!-- 产品5：上边图片，下边文字 -->
            <v-col cols="4" class="pl-2">
              <div class="product-card product-5" @click="navigateToDetail(displayProducts[4])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[4])" :alt="displayProducts[4]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[4]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[4]?.description }}</p>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </div>

      <!-- Mobile Layout -->
      <div class="mobile-layout d-block d-md-none">
        <v-container fluid class="px-3">
          <!-- 产品1：第一行横版大卡片 -->
          <div class="mobile-product-1 mb-3" @click="navigateToDetail(displayProducts[0])">
            <div class="image-section">
              <img :src="getImage(displayProducts[0])" :alt="displayProducts[0]?.planProductName || ''" />
            </div>
            <div class="content-section">
              <h3 class="title">{{ displayProducts[0]?.planProductName }}</h3>
              <p class="description">{{ displayProducts[0]?.description }}</p>
            </div>
          </div>

          <!-- 第二行：两列瀑布流布局 -->
          <div class="mobile-grid">
            <!-- 左列 -->
            <div class="left-column">
              <!-- 产品2 -->
              <div class="mobile-product-2 mb-3" @click="navigateToDetail(displayProducts[1])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[1])" :alt="displayProducts[1]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[1]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[1]?.description }}</p>
                </div>
              </div>

              <!-- 产品3 -->
              <div class="mobile-product-3" @click="navigateToDetail(displayProducts[2])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[2])" :alt="displayProducts[2]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[2]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[2]?.description }}</p>
                </div>
              </div>
            </div>

            <!-- 右列（错位布局） -->
            <div class="right-column">
              <!-- 产品4 -->
              <div class="mobile-product-4 mb-3" @click="navigateToDetail(displayProducts[3])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[3])" :alt="displayProducts[3]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[3]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[3]?.description }}</p>
                </div>
              </div>

              <!-- 产品5 -->
              <div class="mobile-product-5" @click="navigateToDetail(displayProducts[4])">
                <div class="image-section">
                  <img :src="getImage(displayProducts[4])" :alt="displayProducts[4]?.planProductName || ''" />
                </div>
                <div class="content-section">
                  <h3 class="title">{{ displayProducts[4]?.planProductName }}</h3>
                  <p class="description">{{ displayProducts[4]?.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </v-container>
      </div>
    </div>

    <!-- 更多按钮 -->
    <div class="more-btn">
      <v-btn @click="navigateToProductList" variant="outlined">
        もっと見る
      </v-btn>
    </div>
  </div>
</template>
<style scoped lang="scss">
$primary-color: #26499d;
$shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
$radius: 8px;

.product-wrapper {
  max-width:88%;
  margin: 0 auto;
  padding: 20px;

  .section-title {
    text-align: center;
    color: $primary-color;
    margin: 0 0 8px;
    font-size: 18px;
    font-weight: 600;
  }

  .title-underline {
    width: 40px;
    height: 2px;
    background: $primary-color;
    margin: 0 auto 20px;
  }
}

// Loading & Error
.loading, .error {
  text-align: center;
  padding: 40px 20px;

  button {
    margin-top: 10px;
    padding: 8px 16px;
    border: 1px solid $primary-color;
    background: #fff;
    color: $primary-color;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: $primary-color;
      color: #fff;
    }
  }
}

// Base card styles
.product-card {
  background: #fff;
  border-radius: $radius;
  box-shadow: $shadow;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 250px;
  border: 1px solid #fff;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .title {
    font-weight: bold;
    color: #333;
    line-height: 1.3;
    margin: 0 0 8px;
  }

  .description {
    color: #666;
    line-height: 1.4;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// =================== PC Layout ===================
.desktop-layout {
  max-width: 69%;
  margin: 0 auto;

  // 产品1：第一行第一张 - 左边图片，右边文字
  .product-1 {
    display: flex;
    min-height: 150px;

    .image-section {
      flex: 0 0 60%;
    }

    .content-section {
      flex: 1;
      padding: 4px;
      flex-direction: column;
      justify-content: center;
      background-color: #D5E4F0;
    }

    .title {
      font-size: 14px;
    }

    .description {
      font-size: 10px;
    }
  }

  // 产品2：第一行第二张 - 上边图片，下边文字
  .product-2 {
    display: flex;
    flex-direction: column;
    min-height: 180px;

    .image-section {
      height: 75%;
    }

    .content-section {
      flex: 1;
      padding: 4px;
      flex-direction: column;
      justify-content: center;
      background-color: #E2E2E2;
    }

    .title {
      font-size: 14px;
      display: flex;
      justify-content: center;
    }

    .description {
      font-size: 10px;
    }
  }

  // 产品3：第二行第一张 - 上边图片，下边文字
  .product-3 {
    display: flex;
    flex-direction: column;
    min-height: 160px;

    .image-section {
      height: 65%;
    }

    .content-section {
      flex: 1;
      padding: 4px;
      flex-direction: column;
      justify-content: center;
      background-color: #F4E4E1;
    }

    .title {
      display: flex;
      justify-content: center;
      font-size: 14px;
    }

    .description {
      font-size: 10px;
    }
  }

  // 产品4：第二行第二张 - 左边图片，右边文字
  .product-4 {
    display: flex;
    min-height: 160px;

    .image-section {
      flex: 0 0 75%;
    }

    .content-section {
      flex: 1;
      padding: 4px;
      flex-direction: column;
      justify-content: center;
      background-color: #DAEAD2;
    }

    .title {
      display: flex;
      justify-content: center;
      font-size: 14px;
    }

    .description {
      font-size: 10px;
    }
  }

  // 产品5：第二行第三张 - 上边图片，下边文字
  .product-5 {
    display: flex;
    flex-direction: column;
    min-height: 160px;

    .image-section {
      height: 65%;
    }

    .content-section {
      flex: 1;
      padding: 4px;
      flex-direction: column;
      justify-content: center;
      background-color: #BCD9E3;
    }

    .title {
      display: flex;
      justify-content: center;
      font-size: 14px;
    }

    .description {
      font-size: 10px;
    }
  }
}

// =================== Mobile Layout ===================
.mobile-layout {
  // 移动端产品1：第一行大卡片 - 左边图片，右边文字
  .mobile-product-1 {
    display: flex;
    height: 200px;
    background-color: #D5E4F0;
    border-radius: $radius;
    box-shadow: $shadow;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .image-section {
      flex: 0 0 60%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .content-section {
      flex: 1;
      padding: 12px;
      display: flex;
      flex-direction: column;
    }

    .title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
      margin: 0 0 6px;
      line-height: 1.3;
    }

    .description {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  // 移动端网格布局
  .mobile-grid {
    display: flex;
    gap: 12px;
  }

  .left-column, .right-column {
    flex: 1;
  }

  // 移动端垂直卡片基础样式
  .mobile-product-2,
  .mobile-product-5 {
    height: 240px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: $radius;
    box-shadow: $shadow;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .image-section {
      flex: 0 0 68%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .content-section {
      padding: 10px;
    }

    .title {
      display: flex;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: #333;
      margin: 0 0 4px;
      line-height: 1.3;
    }

    .description {
      font-size: 11px;
      color: #666;
      line-height: 1.4;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .mobile-product-3,
  .mobile-product-4 {
    height: 190px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: $radius;
    box-shadow: $shadow;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .image-section {
      flex: 0 0 65%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .content-section {
      padding: 10px;
    }

    .title {
      display: flex;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: #333;
      margin: 0 0 4px;
      line-height: 1.3;
    }

    .description {
      font-size: 11px;
      color: #666;
      line-height: 1.4;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  // 移动端产品2：左列第一张
  .mobile-product-2 {
    background-color: #F4E4E1;
  }

  // 移动端产品3：左列第二张
  .mobile-product-3 {
    background-color: #BCD9E3;
  }

  // 移动端产品4：右列第一张
  .mobile-product-4 {
    background-color: #E2E2E2;
  }

  // 移动端产品5：右列第二张
  .mobile-product-5 {
    background-color: #DAEAD2;
    .image-section {
      height: 85px;
    }
  }
}

// More button
.more-btn {
  text-align: center;
}

// 小屏幕优化
@media (max-width: 480px) {
  .product-wrapper {
    padding: 15px;
    max-width:100%;
  }

  .mobile-layout {
    .mobile-product-1 {
      min-height: 80px;

      .content-section {
        padding: 10px;
      }
    }

    .mobile-product-2,
    .mobile-product-3,
    .mobile-product-4,
    .mobile-product-5 {
      .image-section {
        height: 60px !important;
      }

      .content-section {
        padding: 8px;
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 360px) {
  .mobile-layout {
    max-width:100%;
    .mobile-grid {
      gap: 8px;
    }

    .right-column {
      margin-top: -25px;
    }
  }
}
</style>