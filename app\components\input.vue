<script setup lang="ts">
import { defineProps, computed } from 'vue';

interface DataItem {
    id: number;
    name: string;
    type: string;
}

const props = defineProps<{
    searchResults: DataItem[];
}>();
</script>

<template>
    <div class="resultContainer" v-if="searchResults.length > 0">
        <h6>入力履歴</h6>
        <div v-for="item in searchResults" :key="item.id" class="resultItem">
            ID: {{ item.id }} {{ item.name }} {{ item.type }}
        </div>
    </div>
</template>

<style scoped>
h6 {
    color: #26499D;
    padding: 0 0 10px 16px;
}

.resultContainer {
    overflow-y: scroll;
    padding: 10px;
    border-radius: 4px;
    position: absolute;
    top: 11rem;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 999;
}

.resultItem {
    padding: 16px;
    border-top: 1px solid #DFDFDF;
}

.resultItem:last-child {
    border-bottom: 1px solid #DFDFDF;
}
</style>
