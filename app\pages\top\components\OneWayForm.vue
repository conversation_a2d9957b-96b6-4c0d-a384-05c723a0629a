<script setup lang="ts">
import { computed, inject, watch } from 'vue'
import { useDisplay } from 'vuetify'
import { useFormValidation } from '../../../composables/top/useFormValidation'
import { useDateTimeHandler } from '../../../composables/top/useDateTimeHandler'
import { useLocationSwap, type Waypoint } from '../../../composables/top/useLocationSwap'
import { useWaypoints } from '../../../composables/top/useWaypoints'
import replaceImage from '~/assets/image/replace.png'

// 类型定义
interface OneWayFormData {
  departure: string
  destination: string
  date: string
  time: string
  direction: 'departure' | 'arrival'
  waypoints: Waypoint[]
}

interface SearchData {
  type: string
  departure: string
  destination: string
  waypoints: string[]
  date: string
  time: string
  direction: string
}

interface LocationSwapData {
  departure: string
  destination: string
  waypoints: string[]
}

interface DateTimeHandler {
  datePickerOpen: any
  timePickerOpen: any
  currentDateType: any
  currentTimeType: any
  currentIndex: any
  openDatePicker: (type: string, index?: number) => void
  openTimePicker: (type: string, index?: number) => void
}

// Props 和 Emits
const props = defineProps<{
  modelValue: OneWayFormData
}>()

const emit = defineEmits<{
  'update:modelValue': [value: OneWayFormData]
  search: [data: SearchData]
  locationSwap: [data: LocationSwapData]
}>()

// Composables
const { mobile } = useDisplay()
const { formRef, validationRules, validateForm } = useFormValidation()
const { formatDate, formatTime } = useDateTimeHandler()
const { swapLocations } = useLocationSwap()
const {
  waypoints,
  showWaypoints,
  addWaypoint,
  removeWaypoint,
  toggleWaypoint,
  canAddMoreWaypoints,
  waypointButtonText,
  getValidWaypoints
} = useWaypoints()

// 从父组件注入的日期时间处理方法
const dateTimeHandler = inject<DateTimeHandler>('dateTimeHandler')
if (!dateTimeHandler) {
  throw new Error('dateTimeHandler not provided')
}
const { openDatePicker, openTimePicker } = dateTimeHandler

// 本地数据 - 与父组件双向绑定
const localData = computed<OneWayFormData>({
  get(): OneWayFormData {
    return props.modelValue
  },
  set(value: OneWayFormData): void {
    emit('update:modelValue', value)
  }
})

// 同步 waypoints 到父组件数据
watch(
    waypoints,
    (newWaypoints: Waypoint[]): void => {
      localData.value.waypoints = [...newWaypoints]
    },
    { deep: true }
)

// 处理位置交换
const handleSwapLocations = (): void => {
  const result = swapLocations(
      localData.value.departure,
      localData.value.destination,
      waypoints.value
  )

  localData.value.departure = result.departure
  localData.value.destination = result.destination

  if (result.waypoints) {
    waypoints.value = result.waypoints
  }

  emit('locationSwap', {
    departure: result.departure,
    destination: result.destination,
    waypoints: getValidWaypoints()
  })
}
const dynamicDateRules = computed(() => [
  () => !!localData.value.date || '日付を選択してください'
])

const dynamicTimeRules = computed(() => [
  () => !!localData.value.time || '時間を選択してください'
])
// 处理搜索
const handleSearch = async (): Promise<void> => {
  const { valid } = await validateForm()
  if (!valid) return

  const searchData: SearchData = {
    type: '片道',
    departure: localData.value.departure,
    destination: localData.value.destination,
    waypoints: getValidWaypoints(),
    date: localData.value.date,
    time: localData.value.time,
    direction: localData.value.direction
  }

  emit('search', searchData)
}
</script>
<template>
  <v-form ref="formRef">
    <!-- PC端水平布局 -->
    <div v-if="!mobile" class="pc-horizontal-layout">
      <!-- 左侧：出发地/目的地组 -->
      <div class="location-group">
        <!-- 出发地 -->
        <v-text-field
            v-model="localData.departure"
            :rules="validationRules.departure"
            class="location-input mb-4"
            placeholder="出発地を選択"
            density="comfortable"
            hide-details="auto"
        />

        <!-- 经由地和交换按钮 -->
        <div class="controls-section mb-4">
          <v-btn
              variant="outlined"
              color="#26499D"
              class="waypoint-button"
              prepend-icon="mdi-plus"
              :disabled="showWaypoints && !canAddMoreWaypoints"
              @click="showWaypoints ? addWaypoint() : toggleWaypoint()"
          >
            {{ waypointButtonText }}
          </v-btn>

          <v-btn
              icon
              variant="outlined"
              color="primary"
              size="small"
              class="swap-button"
              @click="handleSwapLocations"
          >
            <img :src="replaceImage" alt="交换位置" class="replace-tivImage"/>
          </v-btn>
        </div>

        <!-- 经由地列表 -->
        <div
            v-if="showWaypoints && waypoints.length > 0"
            class="waypoints-container mb-4"
        >
          <v-fade-transition group>
            <div
                v-for="(waypoint, index) in waypoints"
                :key="waypoint.id"
                class="waypoint-item mb-3"
            >
              <v-row no-gutters align="center">
                <v-col cols="10">
                  <v-text-field
                      v-model="waypoint.location"
                      hide-details
                      class="location-input"
                      :placeholder="`経由地${index + 1}を選択`"
                      variant="outlined"
                      density="comfortable"
                  />
                </v-col>
                <v-col cols="2" class="pl-2">
                  <v-btn
                      color="#7D7D7D"
                      density="compact"
                      icon="mdi-close"
                      @click="removeWaypoint(waypoint.id)"
                  />
                </v-col>
              </v-row>
            </div>
          </v-fade-transition>
        </div>

        <!-- 目的地 -->
        <v-text-field
            v-model="localData.destination"
            :rules="validationRules.destination"
            class="location-input"
            placeholder="到着地を選択"
            variant="outlined"
            density="comfortable"
            hide-details="auto"
        />
      </div>

      <!-- 中间分割线 -->
      <div class="vertical-divider"></div>

      <!-- 右侧：日期时间/出发到着组 -->
      <div class="datetime-group">
        <div class="section-headers">
          <span class="section-title">日付</span>
          <span class="section-title">時間指定（任意）</span>
        </div>

        <!-- 日期和时间选择 -->
        <div class="date-time-inputs mb-4">
          <v-text-field
              v-model="localData.date"
              :value="formatDate(localData.date)"
              :rules="validationRules.date"
              variant="outlined"
              density="comfortable"
              readonly
              class="date-display-pc"
              placeholder="日付を選択"
              hide-details="auto"
              @click="openDatePicker('oneway')"
          >
            <template #append-inner>
              <v-icon color="primary">mdi-calendar</v-icon>
            </template>
          </v-text-field>

          <v-text-field
              v-model="localData.time"
              :rules="validationRules.time"
              variant="outlined"
              density="comfortable"
              readonly
              class="time-display-pc"
              placeholder="指定なし"
              hide-details="auto"
              @click="openTimePicker('oneway')"
          >
            <template #append-inner>
              <v-icon color="primary">mdi-clock</v-icon>
            </template>
          </v-text-field>
        </div>

        <!-- 出发/到着选择 -->
        <v-radio-group
            v-model="localData.direction"
            class="direction-radio-pc"
            hide-details
        >
          <v-radio
              label="出発"
              value="departure"
              color="primary"
              class="radio-item"
          />
          <v-radio
              label="到着"
              value="arrival"
              color="primary"
              class="radio-item"
          />
        </v-radio-group>
      </div>
    </div>

    <!-- 移动端垂直布局 (原有布局) -->
    <div v-else class="mobile-vertical-layout">
      <!-- 出发地 -->
      <v-text-field
          v-model="localData.departure"
          :rules="validationRules.departure"
          class="location-input mb-4"
          placeholder="出発地を選択"
          density="comfortable"
          hide-details="auto"
      />

      <!-- 经由地和交换按钮 -->
      <div class="controls-section mb-4">
        <v-btn
            variant="outlined"
            color="#26499D"
            class="waypoint-button"
            prepend-icon="mdi-plus"
            :disabled="showWaypoints && !canAddMoreWaypoints"
            @click="showWaypoints ? addWaypoint() : toggleWaypoint()"
        >
          {{ waypointButtonText }}
        </v-btn>

        <v-btn
            icon
            variant="outlined"
            color="primary"
            size="small"
            class="swap-button"
            @click="handleSwapLocations"
        >
          <img :src="replaceImage" alt="交换位置" class="replace-tivImage"/>
        </v-btn>
      </div>

      <!-- 经由地列表 -->
      <div
          v-if="showWaypoints && waypoints.length > 0"
          class="waypoints-container mb-4"
      >
        <v-fade-transition group>
          <div
              v-for="(waypoint, index) in waypoints"
              :key="waypoint.id"
              class="waypoint-item mb-3"
          >
            <v-row no-gutters align="center">
              <v-col cols="10">
                <v-text-field
                    v-model="waypoint.location"
                    hide-details
                    class="location-input"
                    :placeholder="`経由地${index + 1}を選択`"
                    variant="outlined"
                    density="comfortable"
                />
              </v-col>
              <v-col cols="2" class="pl-2">
                <v-btn
                    color="#7D7D7D"
                    density="compact"
                    icon="mdi-close"
                    @click="removeWaypoint(waypoint.id)"
                />
              </v-col>
            </v-row>
          </div>
        </v-fade-transition>
      </div>

      <!-- 目的地 -->
      <v-text-field
          v-model="localData.destination"
          :rules="validationRules.destination"
          class="location-input mb-4"
          placeholder="到着地を選択"
          variant="outlined"
          density="comfortable"
          hide-details="auto"
      />

      <!-- 日期和时间选择 -->
      <div class="date-section mb-4">
        <v-row no-gutters>
          <v-col cols="7">
            <v-text-field
                :value="formatDate(localData.date)"
                :rules="validationRules.date"
                variant="outlined"
                density="comfortable"
                readonly
                class="date-display"
                placeholder="日付を選択"
                hide-details="auto"
                @click="openDatePicker('oneway')"
            >
              <template #append-inner>
                <v-icon color="primary">mdi-calendar</v-icon>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="5" class="pl-1">
            <v-text-field
                v-model="localData.time"
                :value="formatTime(localData.time)"
                :rules="validationRules.time"
                variant="outlined"
                density="comfortable"
                readonly
                class="time-display"
                placeholder="時間を選択"
                hide-details="auto"
                @click="openTimePicker('oneway')"
            >
              <template #append-inner>
                <v-icon color="primary">mdi-clock</v-icon>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
      </div>

      <!-- 出发/到着选择 -->
      <v-radio-group
          v-model="localData.direction"
          inline
          class="direction-radio mb-6"
          hide-details
      >
        <v-radio
            label="出発"
            value="departure"
            color="primary"
            class="radio-item"
        />
        <v-radio
            label="到着"
            value="arrival"
            color="primary"
            class="radio-item"
        />
      </v-radio-group>
    </div>

    <!-- 搜索按钮 -->
    <div class="center">
      <v-btn
          size="large"
          color="#ed785f"
          variant="flat"
          class="search-button"
          @click="handleSearch"
      >
        検索
        <v-icon icon="mdi-magnify" class="ml-2"/>
      </v-btn>
    </div>
  </v-form>
</template>
<style scoped>
/* 原有移动端样式 */
.location-input :deep(.v-field) {
  border-radius: 8px;
}

.location-input :deep(.v-field__input) {
  padding: 16px;
  min-height: 56px;
}

.location-input :deep(.v-label) {
  color: #9aa0a6;
  font-size: 16px;
}

.controls-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.swap-button {
  border: 0px #e0e4e7 !important;
}

.replace-tivImage {
  width: 40px;
  height: 40px;
}

.waypoint-button {
  border: 0px dashed #c1c7cd !important;
  color: #666 !important;
  font-size: 13px;
}

.waypoint-button :deep(.v-btn__prepend) {
  margin-inline-end: 8px;
}

.waypoint-button :deep(.v-btn__prepend .v-icon) {
  width: 20px;
  height: 20px;
  background: #26499d;
  color: white;
  border-radius: 50%;
  font-size: 14px;
}

/* 日期选择样式 */
.date-display :deep(.v-field) {
  border-radius: 8px;
}

.date-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

/* 时间选择样式 */
.time-display :deep(.v-field) {
  border-radius: 8px;
}

.time-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

/* 单选按钮样式 */
.direction-radio {
  padding: 16px 0;
}

.direction-radio :deep(.v-selection-control-group) {
  gap: 32px;
}

.radio-item :deep(.v-label) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-left: 8px;
}

.radio-item :deep(.v-selection-control__wrapper) {
  width: 18px;
  height: 18px;
}

.radio-item :deep(.v-radio .v-selection-control__input) {
  width: 18px;
  height: 18px;
}

.search-button {
  width: 330px !important;
  border-radius: 24px !important;
  height: 50px !important;
  font-size: 16px;
}

.search-button :deep(.v-btn__prepend) {
  margin-inline-end: 8px;
}

:deep(.v-field--variant-outlined .v-field__outline) {
  color: #e0e4e7;
}

/* PC端水平布局样式 */
@media (min-width: 960px) {
  .pc-horizontal-layout {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    margin-bottom: 32px;
  }

  .location-group {
    flex: 1;
    min-width: 0;
    padding-top: 20px;
  }

  .vertical-divider {
    width: 1px;
    height: 200px;
    background-color: #e0e4e7;
    margin-top: 20px;
  }

  .datetime-group {
    flex: 1;
    min-width: 0;
  }

  .section-headers {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
  }

  .section-title {
    flex: 1;
    font-size: 14px;
    color: rgba(var(--v-theme-primary));
    font-weight: 500;
  }

  .date-time-inputs {
    display: flex;
    gap: 16px;
  }

  .date-display-pc,
  .time-display-pc {
    flex: 1;
  }

  .date-display-pc :deep(.v-field),
  .time-display-pc :deep(.v-field) {
    border-radius: 8px;
  }

  .date-display-pc :deep(.v-field__input),
  .time-display-pc :deep(.v-field__input) {
    padding: 16px;
    cursor: pointer;
  }

  .direction-radio-pc {
    padding: 16px 0;
  }

  .direction-radio-pc :deep(.v-selection-control) {
    flex: none !important;
  }

  .direction-radio-pc :deep(.v-selection-control-group) {
    gap: 32px;
    flex-direction: row;
  }

  .direction-radio-pc .radio-item {
    margin-bottom: 8px;
  }
}

/* 确保移动端样式不受影响 */
@media (max-width: 959px) {
  .mobile-vertical-layout {
    /* 保持原有移动端布局 */
  }

  .search-button {
    width: 330px !important;
    margin: 0 auto;
  }
}
</style>
