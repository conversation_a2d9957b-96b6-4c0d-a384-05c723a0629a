<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { useDisplay } from 'vuetify'
import { useFormValidation } from '../../../composables/top/useFormValidation'
import { useDateTimeHandler } from '../../../composables/top/useDateTimeHandler'

// 类型定义
interface ItineraryItem {
  departure: string
  destination: string
  date: string
  time: string
  tripType: 'departure' | 'arrival'
}

interface MyRouteFormData {
  itineraries: ItineraryItem[]
}

interface SearchData {
  type: string
  itineraries: ItineraryItem[]
}

interface ValidationResult {
  valid: boolean
  message?: string
}

interface DateTimeHandler {
  datePickerOpen: any
  timePickerOpen: any
  currentDateType: any
  currentTimeType: any
  currentIndex: any
  openDatePicker: (type: string, index?: number) => void
  openTimePicker: (type: string, index?: number) => void
}

// 图片资源
import mistakeImage from '~/assets/image/mistake.png'
import replaceImage from '~/assets/image/replace.png'
import questionImage from '~/assets/image/question.png'
import ellipseImage from '~/assets/image/Ellipse.png'

// Props 和 Emits
const props = defineProps<{
  modelValue: MyRouteFormData
}>()

const emit = defineEmits<{
  'update:modelValue': [value: MyRouteFormData]
  search: [data: SearchData]
}>()

// Composables
const { mobile } = useDisplay()
const { formRef, validationRules, validateForm } = useFormValidation()
const { formatDate, formatTime } = useDateTimeHandler()

// 从父组件注入的日期时间处理方法
const dateTimeHandler = inject<DateTimeHandler>('dateTimeHandler')
if (!dateTimeHandler) {
  throw new Error('dateTimeHandler not provided')
}
const { openDatePicker, openTimePicker } = dateTimeHandler

// 帮助信息弹窗状态
const guideOpen = ref<boolean>(false)

// 本地数据 - 与父组件双向绑定
const localData = computed<MyRouteFormData>({
  get(): MyRouteFormData {
    return props.modelValue
  },
  set(value: MyRouteFormData): void {
    emit('update:modelValue', value)
  }
})

// 添加新行程
const addItinerary = (): void => {
  localData.value.itineraries.push({
    departure: '',
    destination: '',
    date: '',
    time: '',
    tripType: 'departure'
  })
}

// 移除行程
const removeItinerary = (index: number): void => {
  if (localData.value.itineraries.length > 1) {
    localData.value.itineraries.splice(index, 1)
  }
}

// 交换出发地和目的地
const swapLocations = (index: number): void => {
  const item = localData.value.itineraries[index]
  const temp = item.departure
  item.departure = item.destination
  item.destination = temp
}

// 打开帮助信息
const openGuide = (): void => {
  guideOpen.value = true
}

// 关闭帮助信息
const closeGuide = (): void => {
  guideOpen.value = false
}

// 验证所有行程
const validateAllItineraries = (): ValidationResult => {
  for (let i = 0; i < localData.value.itineraries.length; i++) {
    const item = localData.value.itineraries[i]
    if (!item.departure) {
      return {
        valid: false,
        message: `ルート${i + 1}の出発地を選択してください`
      }
    }
    if (!item.destination) {
      return {
        valid: false,
        message: `ルート${i + 1}の到着地を選択してください`
      }
    }
    if (!item.date) {
      return { valid: false, message: `ルート${i + 1}の日付を選択してください` }
    }
    if (!item.time) {
      return { valid: false, message: `ルート${i + 1}の時間を選択してください` }
    }
  }
  return { valid: true }
}

// 处理搜索
const handleSearch = async (): Promise<void> => {
  // 先验证表单基本结构
  const { valid } = await validateForm()
  if (!valid) return

  // 验证所有行程数据
  const itineraryValidation = validateAllItineraries()
  if (!itineraryValidation.valid) {
    alert(itineraryValidation.message)
    return
  }

  const searchData: SearchData = {
    type: 'Myルート作成',
    itineraries: localData.value.itineraries.map((item: ItineraryItem) => ({
      departure: item.departure,
      destination: item.destination,
      date: item.date,
      time: item.time,
      tripType: item.tripType
    }))
  }

  emit('search', searchData)
}
</script>

<template>
  <!-- 标题和帮助按钮 -->
  <v-card-title class="text-center" @click="openGuide">
    <img :src="questionImage" alt="help" class="question-icon"/>
    My ルート情報入力
  </v-card-title>

  <!-- 帮助信息弹窗 -->
  <v-overlay v-model="guideOpen" class="align-center justify-center">
    <div class="my-route-guide">
      <h3 class="create-title">Myルート作成とは</h3>
      <div class="text-container">
        <p class="create-content">
          自分の行きたい経由地を繋げることで、あなただけのオリジナルの旅程を作ることができます。
        </p>
        <ol class="create-content">
          <li>①ルート1の出発地を選択します。</li>
          <li>
            ②到着地の候補は出発地からの直行便のあるエリアだけの表示になります。
          </li>
          <li>③出発日を選択ください。</li>
          <li>④出発/到着時間の希望があれば指定してください。</li>
          <li>⑤ルート2もルート1と同じ手順で入力してください。</li>
          <li>⑥希望のルートを設定できたら「検索」ボタンを押下ください。</li>
        </ol>
        <p class="create-content">
          ※ルート2の出発地はルート1の到着エリア、バス停と同じ場所にする必要はありません。乗降場所が違う場合はその間の移動はお客様自身で計画(移動)してください。
        </p>
        <p class="create-content">※ルート2の出発日は、ルート1と違う日の設</p>
      </div>
      <div @click="closeGuide" class="close-button">閉じる</div>
    </div>
  </v-overlay>

  <div>
    <v-form ref="formRef">
      <!-- 行程卡片列表 -->
      <div
          v-for="(item, index) in localData.itineraries"
          :key="index"
          class="itinerary-item mb-6 p-4 bg-gray-50 rounded-lg relative"
      >
        <div class="itinerary-header">
          <div class="route-number">ルート {{ index + 1 }}</div>
          <div>
            <img
                v-if="localData.itineraries.length > 1"
                :src="mistakeImage"
                alt="删除"
                @click="removeItinerary(index)"
                class="remove-icon"
            />
          </div>
        </div>

        <!-- PC端水平布局 -->
        <div v-if="!mobile" class="pc-horizontal-layout">
          <!-- 左侧：出发地/目的地组 -->
          <div class="location-group">
            <!-- 出发地 -->
            <v-text-field
                v-model="item.departure"
                :rules="validationRules.departure"
                class="location-input"
                placeholder="出発地を選択"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
            />
            <!-- 交换按钮 -->
            <div class="controls-section">
              <v-btn
                  icon
                  variant="outlined"
                  color="primary"
                  size="small"
                  @click="swapLocations(index)"
                  class="swap-button"
              >
                <img :src="replaceImage" alt="交换位置" class="replace-image"/>
              </v-btn>
            </div>
            <!-- 目的地 -->
            <v-text-field
                v-model="item.destination"
                :rules="validationRules.destination"
                class="location-input"
                placeholder="到着地を選択"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
            />
          </div>

          <!-- 中间分割线 -->
          <div class="vertical-divider"></div>

          <!-- 右侧：日期时间/出发到着组 -->
          <div class="datetime-group">
            <div class="section-headers">
              <span class="section-title">日付</span>
              <span class="section-title">時間指定（任意）</span>
            </div>

            <!-- 日期和时间选择 -->
            <div class="date-time-inputs mb-4">
              <v-text-field
                  :value="formatDate(item.date)"
                  :rules="validationRules.date"
                  variant="outlined"
                  density="comfortable"
                  readonly
                  class="date-display-pc"
                  placeholder="日付を選択"
                  hide-details="auto"
                  @click="openDatePicker('myroute', index)"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-calendar</v-icon>
                </template>
              </v-text-field>

              <v-text-field
                  :value="formatTime(item.time)"
                  :rules="validationRules.time"
                  variant="outlined"
                  density="comfortable"
                  readonly
                  class="time-display-pc"
                  placeholder="指定なし"
                  hide-details="auto"
                  @click="openTimePicker('myroute', index)"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-clock</v-icon>
                </template>
              </v-text-field>
            </div>

            <!-- 出发/到着选择 -->
            <v-radio-group
                v-model="item.tripType"
                class="direction-radio-pc"
                hide-details
            >
              <v-radio
                  label="出発"
                  value="departure"
                  color="primary"
                  class="radio-item"
              />
              <v-radio
                  label="到着"
                  value="arrival"
                  color="primary"
                  class="radio-item"
              />
            </v-radio-group>
          </div>
        </div>

        <!-- 移动端垂直布局 (原有布局) -->
        <div v-else class="mobile-vertical-layout">
          <!-- 出发地 -->
          <v-text-field
              v-model="item.departure"
              :rules="validationRules.departure"
              class="location-input"
              placeholder="出発地を選択"
              variant="outlined"
              density="comfortable"
              hide-details="auto"
          />

          <!-- 交换按钮 -->
          <div class="controls-section">
            <v-btn
                icon
                variant="outlined"
                color="primary"
                size="small"
                @click="swapLocations(index)"
                class="createMargin"
            >
              <img :src="replaceImage" alt="交换位置" class="replace-image"/>
            </v-btn>
          </div>

          <!-- 目的地 -->
          <v-text-field
              v-model="item.destination"
              :rules="validationRules.destination"
              class="location-input"
              placeholder="到着地を選択"
              variant="outlined"
              density="comfortable"
              hide-details="auto"
          />

          <!-- 日期和时间选择 -->
          <div class="date-section">
            <v-row no-gutters>
              <v-col cols="7">
                <v-text-field
                    v-model="item.date"
                    :value="formatDate(item.date)"
                    :rules="validationRules.date"
                    variant="outlined"
                    density="comfortable"
                    readonly
                    class="date-display"
                    placeholder="日付を選択"
                    hide-details="auto"
                    @click="openDatePicker('myroute', index)"
                >
                  <template #append-inner>
                    <v-icon color="primary">mdi-calendar</v-icon>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="5" class="pl-1">
                <v-text-field
                    v-model="item.time"
                    :rules="validationRules.time"
                    variant="outlined"
                    density="comfortable"
                    readonly
                    class="time-display"
                    placeholder="時間を選択"
                    hide-details="auto"
                    @click="openTimePicker('myroute', index)"
                >
                  <template #append-inner>
                    <v-icon color="primary">mdi-clock</v-icon>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
          </div>

          <!-- 出发/到着选择 -->
          <v-radio-group
              v-model="item.tripType"
              inline
              class="direction-radio mb-6"
              hide-details
          >
            <v-radio
                label="出発"
                value="departure"
                color="primary"
                class="radio-item"
            />
            <v-radio
                label="到着"
                value="arrival"
                color="primary"
                class="radio-item"
            />
          </v-radio-group>
        </div>
      </div>

      <!-- 添加行程按钮 -->
      <div @click="addItinerary" class="add-route-btn">
        <img :src="ellipseImage" alt="添加" class="ellipsIcon"/>
        ルートを追加する
      </div>

      <!-- 搜索按钮 -->
      <div class="center">
        <v-btn
            size="large"
            color="#ed785f"
            variant="flat"
            class="search-button"
            @click="handleSearch"
        >
          検索
          <v-icon icon="mdi-magnify" class="ml-2"/>
        </v-btn>
      </div>
    </v-form>
  </div>
</template>

<style lang="scss" scoped>
.my-route-guide {
  width: 300px;
  padding: 16px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #fff;
}

.text-container {
  max-height: 457px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.close-button {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #9cbcd4;
  background-color: #e7f2fa;
  color: #26499d;
  text-align: center;
  cursor: pointer;
}

.text-center {
  font-size: 14px;
  color: #26499d;
  display: flex;
  justify-content: end;
  cursor: pointer;
}

.question-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.itinerary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.route-number {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #26499d;
  font-weight: 600;
}

.remove-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.itinerary-item {
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

/* 输入框样式 */
.location-input :deep(.v-field) {
  border-radius: 8px;
  margin-bottom: 12px;
}

.location-input :deep(.v-field__input) {
  padding: 16px;
  min-height: 56px;
}

.controls-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;
}

.swap-button {
  border: none !important;
}

.replace-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.date-section {
  padding: 4px 0;
  margin-bottom: 12px;
}

/* 日期选择样式 */
.date-display :deep(.v-field) {
  border-radius: 8px;
}

.date-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

/* 时间选择样式 */
.time-display :deep(.v-field) {
  border-radius: 8px;
}

.time-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

/* 单选按钮样式 */
.direction-radio {
  padding: 8px 0;
  border-bottom: 1px solid #dfdfdf;

  :deep(.v-selection-control-group) {
    gap: 24px;
  }

  :deep(.v-selection-control) {
    flex: none;
  }

  .radio-item {
    :deep(.v-label) {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-left: 8px;
    }
  }
}

.add-route-btn {
  color: #000 !important;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
    border-color: #26499d;
  }

  .ellipsIcon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}

.search-button {
  width: 330px !important;
  border-radius: 24px !important;
  height: 50px !important;
  font-size: 16px;
  background: #ed785f !important;
}

.create-title {
  font-weight: 500;
  font-size: 20px;
  line-height: 1.6;
  margin-bottom: 8px;
}

.create-content {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* 清理默认样式 */
:deep(.v-card) {
  box-shadow: none;
}

:deep(.v-card-text) {
  padding: 0;
}

:deep(.v-btn--variant-outlined) {
  border: none;
}

:deep(.v-field--variant-outlined .v-field__outline) {
  color: #e0e4e7;
}

/* PC端水平布局样式 */
@media (min-width: 960px) {
  .pc-horizontal-layout {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    margin-bottom: 24px;
  }

  .location-group {
    flex: 1;
    min-width: 0;
    padding-top: 20px;
  }

  .vertical-divider {
    width: 1px;
    height: 200px;
    background-color: #e0e4e7;
    margin-top: 20px;
  }

  .datetime-group {
    flex: 1;
    min-width: 0;
  }

  .section-headers {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
  }

  .section-title {
    flex: 1;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-align: center;
  }

  .date-time-inputs {
    display: flex;
    gap: 16px;
  }

  .date-display-pc,
  .time-display-pc {
    flex: 1;
  }

  .date-display-pc :deep(.v-field),
  .time-display-pc :deep(.v-field) {
    border-radius: 8px;
  }

  .date-display-pc :deep(.v-field__input),
  .time-display-pc :deep(.v-field__input) {
    padding: 16px;
    cursor: pointer;
  }

  .direction-radio-pc {
    padding: 16px 0;
  }
  .direction-radio-pc :deep(.v-selection-control) {
    flex: none !important;
  }
  .direction-radio-pc :deep(.v-selection-control-group) {
    gap: 32px;
    flex-direction: row;
  }

  .direction-radio-pc .radio-item {
    margin-bottom: 8px;
  }

  .itinerary-item {
    padding: 24px;
  }

  .location-input :deep(.v-field__input) {
    padding: 18px;
  }
}

/* 确保移动端样式不受影响 */
@media (max-width: 959px) {
  .mobile-vertical-layout {
    /* 保持原有移动端布局 */
  }

  .search-button {
    width: 330px !important;
    margin: 0 auto;
  }
}
</style>
