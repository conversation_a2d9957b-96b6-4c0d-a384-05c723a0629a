// pages/top/composables/useDateTimeHandler.ts
import { ref, type Ref } from 'vue'

export interface DateTimeHandler {
  datePickerOpen: Ref<boolean>
  timePickerOpen: Ref<boolean>
  currentDateType: Ref<string>
  currentTimeType: Ref<string>
  currentIndex: Ref<number>
  formatDate: (dateString: string) => string
  formatTime: (timeString: string) => string
  openDatePicker: (type: string, index?: number) => void
  openTimePicker: (type: string, index?: number) => void
  closeDatePicker: () => void
  closeTimePicker: () => void
  adjustReturnDate: (outboundDate: string, currentReturnDate: string) => string
}

export function useDateTimeHandler(): DateTimeHandler {
  const datePickerOpen = ref<boolean>(false)
  const timePickerOpen = ref<boolean>(false)
  const currentDateType = ref<string>('')
  const currentTimeType = ref<string>('')
  const currentIndex = ref<number>(-1) // 用于 MyRoute 多个行程的索引

  // 日期格式化函数
  const formatDate = (dateString: string): string => {
    if (!dateString) return ''
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const days = ['日', '月', '火', '水', '木', '金', '土']
    const dayOfWeek = days[date.getDay()]
    return `${year}/${month}/${day} (${dayOfWeek})`
  }

  // 时间格式化函数
  const formatTime = (timeString: string): string => {
    if (!timeString) return ''
    if (timeString === 'none') return '指定なし'
    return timeString
  }

  // 打开日期选择器
  const openDatePicker = (type: string, index: number = -1): void => {
    currentDateType.value = type
    currentIndex.value = index
    datePickerOpen.value = true
  }

  // 打开时间选择器
  const openTimePicker = (type: string, index: number = -1): void => {
    currentTimeType.value = type
    currentIndex.value = index
    timePickerOpen.value = true
  }

  // 关闭日期选择器
  const closeDatePicker = (): void => {
    datePickerOpen.value = false
    currentDateType.value = ''
    currentIndex.value = -1
  }

  // 关闭时间选择器
  const closeTimePicker = (): void => {
    timePickerOpen.value = false
    currentTimeType.value = ''
    currentIndex.value = -1
  }

  // 处理往復日期的自动调整
  const adjustReturnDate = (outboundDate: string, currentReturnDate: string): string  => {
    if (currentReturnDate && new Date(currentReturnDate) <= new Date(outboundDate)) {
      const nextDay = new Date(outboundDate)
      nextDay.setDate(nextDay.getDate() + 1)
      return nextDay.toISOString().split('T')[0] || ''
    }
    return currentReturnDate || ''
  }

  return {
    datePickerOpen,
    timePickerOpen,
    currentDateType,
    currentTimeType,
    currentIndex,
    formatDate,
    formatTime,
    openDatePicker,
    openTimePicker,
    closeDatePicker,
    closeTimePicker,
    adjustReturnDate
  }
}