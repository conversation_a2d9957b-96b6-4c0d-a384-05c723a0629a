import type {
  AxiosError,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios'
import axios from 'axios'
import { ref } from 'vue'
import { getToken, setToken, TokenPrefix } from '~/utils/auth'
import type { BaseResponse } from '~/types'
import { refreshToken } from '~/composables/useAuth'

const globalIsLoading = ref(false)
let globalActiveRequests = 0

export const useHttp = () => {
  let isRefreshing = false
  let failedQueue: Array<{
    resolve: (token: string) => void
    reject: (error: unknown) => void
  }> = []

  const processQueue = (error: unknown, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token!)
      }
    })
    failedQueue = []
  }

  enum LoadingAction {
    START = 'start',
    END = 'end',
  }

  const updateLoadingState = (action: LoadingAction) => {
    if (action === LoadingAction.START) {
      if (globalActiveRequests === 0) globalIsLoading.value = true
      globalActiveRequests++
    } else if (action === LoadingAction.END) {
      globalActiveRequests--
      if (globalActiveRequests === 0) globalIsLoading.value = false
    }
  }

  const service = axios.create({
    baseURL: import.meta.env.VITE_BUS_API_BASE_URL || '/api',
    timeout: 5000,
  })

  service.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      const token = getToken()
      if (token) {
        config.headers.authorization = `${TokenPrefix} ${token}`
      }

      updateLoadingState(LoadingAction.START)
      return config
    },
    (error: AxiosError) => {
      updateLoadingState(LoadingAction.END)
      console.error('リクエストエラー:', error)
      return Promise.reject(error)
    }
  )

  service.interceptors.response.use(
    (response: AxiosResponse) => {
      updateLoadingState(LoadingAction.END)

      if (response.status !== 200) {
        console.error('業務エラー:', response.statusText)
        return Promise.reject(new Error(response.statusText || 'Error'))
      }

      return {
        ...response,
        data: response.data,
      } as AxiosResponse<BaseResponse>
    },

    async (error: AxiosError) => {
      updateLoadingState(LoadingAction.END)

      const originalRequest = error.config as InternalAxiosRequestConfig & {
        _retry?: boolean
        _csrfRetry?: boolean
      }
      const status = error.response?.status
      console.log('status', status)

      if (status === 400) {
        console.error('エラー:', error.message)
        return Promise.reject(error)
      }

      if (status === 401) {
        clearCacheRedirect('/login')
        return Promise.reject(error)
      }

      if (status === 402) {
        clearCacheRedirect('/expired')
        return Promise.reject(error)
      }

      if (status === 403) {
        console.error('権限エラー:', error.message)
        return Promise.reject(error)
      }

      if (status === 419 && !originalRequest._retry) {
        return handleTokenRefresh(originalRequest)
      }

      if (status === 500) {
        console.error('システムエラー:', error.message)
        return Promise.reject(error)
      }

      if (error.code == 'ECONNABORTED') {
        console.error('Timeout:', error.message)
        return Promise.reject(error)
      }

      console.error('レスポンスエラー:', error.message)
      return Promise.reject(error)
    }
  )

  const handleTokenRefresh = async (
    originalRequest: InternalAxiosRequestConfig & { _retry?: boolean }
  ) => {
    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject })
      })
        .then((token) => {
          originalRequest.headers.authorization = `${TokenPrefix} ${token}`
          return service(originalRequest)
        })
        .catch((err) => {
          Promise.reject(err instanceof Error ? err : new Error(String(err)))
        })
    }

    originalRequest._retry = true
    isRefreshing = true

    try {
      const refreshResponse = await refreshToken()

      if (refreshResponse.data.code === 401) {
        clearCacheRedirect('/expired')
        return Promise.reject(new Error('Token expired'))
      }

      if (refreshResponse.data.code === 200) {
        const newToken = refreshResponse?.data?.data

        setToken(newToken)

        service.defaults.headers.common['authorization'] = `${TokenPrefix} ${newToken}`

        originalRequest.headers.authorization = `${TokenPrefix} ${newToken}`

        processQueue(null, newToken)

        return service(originalRequest)
      } else {
        clearCacheRedirect('/login')
        return Promise.reject(
          new Error(refreshResponse?.data?.msg || 'トークンの更新に失敗しました')
        )
      }
    } catch (refreshError) {
      processQueue(refreshError, null)
      clearCacheRedirect('/login')
      return Promise.reject(
        refreshError instanceof Error ? refreshError : new Error(String(refreshError))
      )
    } finally {
      isRefreshing = false
    }
  }

  const clearCacheRedirect = (path?: string) => {
    setToken('')
    if (path) {
      navigateTo(path)
    }
  }

  const request = async <T = any>(config: AxiosRequestConfig): Promise<BaseResponse<T>> => {
    return await service.request<any, BaseResponse<T>>(config)
  }

  function get<T = any>(config: AxiosRequestConfig) {
    return request<T>({ ...config, method: 'GET' })
  }

  function post<T = any>(config: AxiosRequestConfig) {
    return request<T>({ ...config, method: 'POST' })
  }

  function put<T = any>(config: AxiosRequestConfig) {
    return request<T>({ ...config, method: 'PUT' })
  }

  function del<T = any>(config: AxiosRequestConfig) {
    return request<T>({ ...config, method: 'DELETE' })
  }

  return {
    isLoading: globalIsLoading,
    get,
    post,
    put,
    del,
    request,
  }
}
