<template>
  <div class="map-search-section">
    <div class="content-overlay">
      <!-- 标题 -->
      <h2 class="section-title">地図から探そう！</h2>

      <!-- 描述文字 -->
      <p class="section-description">
        地図から行きたい場所を見つけてバスを予約できます。
      </p>

      <!-- 搜索按钮 -->
      <v-btn
          color="#ED785F"
          size="large"
          class="search-button"
          @click="handleMapSearch"
          append-icon="mdi-map-search"
      >
        地図から検索
      </v-btn>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "#vue-router";

const router = useRouter();

const handleMapSearch = () => {
  router.push(`/map-search`);
};
</script>

<style scoped lang="scss">
.map-search-section {
  width: 58%;
  min-height: 200px;
  background-image: url('~/assets/image/mapSearch.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
}

.content-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 24px;
  color: #2c5aa0;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.section-description {
  font-size: 16px;
  color: #2c5aa0;
  margin: 0 0 24px 0;
  line-height: 1.5;
  max-width: 500px;
}

.search-button {
  min-width: 330px;
  border-radius: 25px !important;
  color: white !important;
  text-transform: none;
  padding: 0 32px;
  min-height: 50px;
  box-shadow: 0 4px 12px rgba(255, 127, 80, 0.3);
  font-size: 16px;

  &:hover {
    box-shadow: 0 6px 20px rgba(255, 127, 80, 0.4);
    transform: translateY(-2px);
  }
}

// PC端样式调整
@media (min-width: 768px) {
  .map-search-section {
    min-height: 240px;
  }

  .content-overlay {
    padding: 50px 40px;
    min-height: 240px;
  }

  .section-title {
    font-size: 28px;
    margin-bottom: 20px;
  }

  .section-description {
    font-size: 18px;
    margin-bottom: 32px;
  }

  .search-button {
    min-height: 56px;
    font-size: 18px;
    padding: 0 40px;
  }
}

// 移动端样式调整
@media (max-width: 767px) {
  .map-search-section {
    width: 100%;
    min-height: 180px;
    border-radius: 8px;
  }

  .content-overlay {
    padding: 30px 16px;
    min-height: 180px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .section-description {
    max-width: 200px;
    font-size: 14px;
    margin-bottom: 20px;
    br {
      display: none; // 移动端不换行
    }
  }

  .search-button {
    padding: 0 24px;
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .content-overlay {
    padding: 24px 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-description {
    font-size: 13px;
  }
}
</style>