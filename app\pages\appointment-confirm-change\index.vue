<template>
  <div class="appointment-confirm-change">
    <!-- ヘッダー -->
    <v-app-bar color="white" elevation="1" height="50" class="border-b">
      <v-btn icon variant="text" color="primary" @click="$router.back()">
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>

      <v-spacer />

      <v-app-bar-title class="text-h6 font-weight-regular text-center">
        予約確認・変更・キャンセル
      </v-app-bar-title>

      <v-spacer />

      <div style="width: 48px"></div>
    </v-app-bar>

    <!-- コンテンツエリア -->
    <div class="content-area">
      <!-- タブ切替 -->
      <div class="tab-container">
        <v-btn-toggle
          v-model="activeTab"
          mandatory
          variant="outlined"
          divided
          class="custom-tab-toggle"
        >
          <v-btn
            value="scheduled"
            class="tab-button"
            :class="{ 'tab-active': activeTab === 'scheduled' }"
          >
            乗車予定
          </v-btn>
          <v-btn
            value="completed"
            class="tab-button"
            :class="{ 'tab-active': activeTab === 'completed' }"
          >
            乗車済・キャンセル済
          </v-btn>
        </v-btn-toggle>
      </div>

      <!-- タブコンテンツ -->
      <div class="tab-content">
        <ScheduledRides v-if="activeTab === 'scheduled'" />
        <CompletedRides v-if="activeTab === 'completed'" />
      </div>
    </div>

    <!-- フッター -->
    <v-footer class="footer-copyright">
      <div class="text-center w-100">
        <span class="text-caption text-grey-5">
          Copyright 2025 Ryobi bus CO,LTD
        </span>
      </div>
    </v-footer>
  </div>
</template>

<script setup lang="ts">
// インポート
import ScheduledRides from './components/ScheduledRides.vue'
import CompletedRides from './components/CompletedRides.vue'

// リアクティブデータ
const activeTab = ref<'scheduled' | 'completed'>('scheduled')

// メタデータ設定
definePageMeta({
  layout: false
})
</script>

<style scoped>
.appointment-confirm-change {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  padding-bottom: 90px; /* フッター分の余白 */
}

.tab-container {
  padding: 8px 16px;
  background-color: #ffffff;
}

.custom-tab-toggle {
  width: 100%;
  height: 40px;
}

.tab-button {
  flex: 1;
  height: 40px;
  font-size: 14px;
  font-weight: 400;
  color: #272727;
  border: 1px solid #dfdfdf;
}

.tab-button:first-child {
  border-radius: 4px 0 0 4px;
}

.tab-button:last-child {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.tab-button.tab-active {
  background-color: #acd1ed;
  color: #000000;
  font-weight: 500;
}

.tab-content {
  background-color: #ffffff;
}

.footer-copyright {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 90px;
  background-color: #9cbcd4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 22px 0 51px;
}

.border-b {
  border-bottom: 0.5px solid #dfdfdf;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.15);
}
</style>
