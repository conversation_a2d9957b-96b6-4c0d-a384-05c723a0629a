<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { onMounted, ref, computed, watchEffect, nextTick, onUnmounted } from 'vue'
import { useSpotStore, useOriginStore, useWaypointStore } from '~/stores/edit';
import OneWayRoundTripRouteSelect from '~/components/OneWayRoundTripRouteSelect.vue'

const spotStore = useSpotStore();
const originStore = useOriginStore();
const waypointStore = useWaypointStore();

const router = useRouter()
const route = useRoute()
const touristData = ref<any>(null)

interface TripInfo {
  date: string
  time: string
  direction: 'departure' | 'arrival'
}

interface SearchFormData {
  tripType: 'oneway' | 'roundtrip'
  departure: string
  departureId: string
  departureType: '' | 'prefecture' | 'area' | 'busStop'
  destination: string
  destinationId: string | number
  destinationType: '' | 'prefecture' | 'area' | 'busStop'
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: '' | 'prefecture' | 'area' | 'busStop'
  }>
  date?: string
  time?: string
  direction?: 'departure' | 'arrival'
  outbound?: TripInfo
  return?: TripInfo
}

interface LocationData {
  name: string
  id: string | number
  type?: 'prefecture' | 'area' | 'busStop'
  coordinates?: { lat: number; lng: number }
  busStopInfo?: string
  parentId?: string | number
  level?: number
  children?: LocationData[]
  hasChildren?: boolean
}

interface SelectedLocation extends LocationData {
  type: 'prefecture' | 'area' | 'busStop'
}

// 精简的类型检测器
class LocationTypeDetector {
  static detectLocationType(location: LocationData | null): '' | 'prefecture' | 'area' | 'busStop' {
    if (!location) return '';
    if (location.type) return location.type;
    
    if (this.isBusStop(location)) return 'busStop';
    if (this.isArea(location)) return 'area';
    return 'prefecture';
  }

  private static isBusStop(location: LocationData): boolean {
    return !!(
      location.busStopInfo ||
      this.hasStopKeywords(location.name) ||
      (!location.hasChildren && location.level && location.level >= 3)
    );
  }

  private static isArea(location: LocationData): boolean {
    return !!(
      (location.hasChildren && location.parentId) ||
      (location.level === 2) ||
      this.hasAreaKeywords(location.name)
    );
  }

  private static hasStopKeywords(name: string): boolean {
    return /駅|站|バス停|停留所|乗り場|前|口/.test(name);
  }

  private static hasAreaKeywords(name: string): boolean {
    return /区|區|町|市|村|郡|地区|地區/.test(name);
  }
}

// 表单数据
const formData = ref<SearchFormData>({
  tripType: 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure'
})

// 选中位置状态
const selectedLocations = ref({
  departure: null as SelectedLocation | null,
  destination: null as SelectedLocation | null,
  waypoints: [] as SelectedLocation[]
})

// 防止递归更新的标志
let isUpdatingForm = false;

// 使用计算属性来监听 waypoint 变化，避免深度监听
const waypointsCount = computed(() => waypointStore.getWaypointCount);
const currentWaypoints = computed(() => waypointStore.getAllWaypoints);

// 优化后的同步函数 - 带变化检测
const syncWaypointsToForm = () => {
  if (isUpdatingForm) {
    return;
  }

  const waypoints = currentWaypoints.value;
  
  if (!waypoints || waypoints.length === 0) {
    if (formData.value.waypoints.length === 0) {
      return;
    }
    formData.value.waypoints = [];
    selectedLocations.value.waypoints = [];
    return;
  }
  
  // 检查是否真的需要更新
  const currentFormWaypoints = formData.value.waypoints;
  if (currentFormWaypoints.length === waypoints.length) {
    const needsUpdate = waypoints.some((waypoint, index) => {
      const formWaypoint = currentFormWaypoints[index];
      return !formWaypoint || 
             formWaypoint.locationId !== waypoint.id?.toString() || 
             formWaypoint.location !== waypoint.name;
    });
    
    if (!needsUpdate) {
      return;
    }
  }
  
  // 执行更新
  const formattedWaypoints = waypoints.map((waypoint, index) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint);
    return {
      id: waypoint.id?.toString() || '',
      location: waypoint.name || '',
      locationId: waypoint.id?.toString() || '',
      locationType: detectedType
    };
  });
  
  isUpdatingForm = true;
  
  formData.value = {
    ...formData.value,
    waypoints: formattedWaypoints
  };
  
  updateSelectedWaypoints(waypoints);
  
  nextTick(() => {
    isUpdatingForm = false;
  });
}

// 优化后的选中位置更新函数
const updateSelectedWaypoints = (waypoints: LocationData[]) => {
  selectedLocations.value.waypoints = waypoints.map((waypoint, index) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint);
    return {
      name: waypoint.name || '',
      id: waypoint.id?.toString() || '',
      type: detectedType as 'prefecture' | 'area' | 'busStop',
      coordinates: waypoint.coordinates || { lat: 0, lng: 0 },
      parentId: waypoint.parentId,
      level: waypoint.level,
      hasChildren: waypoint.hasChildren,
      children: waypoint.children,
      busStopInfo: waypoint.busStopInfo
    };
  });
}

const selectedSpot = computed(() => spotStore.getSelectedSpot);

// 使用 watchEffect 替代深度监听，更精确控制
watchEffect(() => {
  if (isUpdatingForm) return;
  
  const count = waypointsCount.value;
  const waypoints = currentWaypoints.value;
  
  nextTick(() => {
    syncWaypointsToForm();
  });
});

// 事件处理函数
function handleBack() {
  
  // 先停止监听更新
  isUpdatingForm = true;
  
  router.go(-1);
  
  // 延迟清理，确保页面已切换
  setTimeout(() => {
    originStore.clearCurrentOriginData();
    spotStore.clearCurrentTouristData();
    waypointStore.clearAllWaypoints();
    
    // 重置表单
    Object.assign(formData.value, {
      departure: '',
      departureId: '',
      departureType: '',
      destination: '',
      destinationId: '',
      destinationType: '',
      waypoints: []
    });
    
    selectedLocations.value = {
      departure: null,
      destination: null,
      waypoints: []
    };
    
  }, 100);
}

const handleSearch = async (searchData: SearchFormData): Promise<void> => {
  console.log('🔍 搜索数据:', searchData);
}


// 方案 1: 完全移除 handleFormChange 中对 formData 的修改
const handleFormChange = (newFormData: SearchFormData) => {
  if (isUpdatingForm) return;
  
  // 只处理经由地删除操作
  if (newFormData.waypoints && newFormData.waypoints.length < selectedLocations.value.waypoints.length) {
    selectedLocations.value.waypoints = selectedLocations.value.waypoints.slice(0, newFormData.waypoints.length);
  }
  
  // 如果需要更新其他表单字段（非经由地），单独处理
  if (newFormData.tripType !== formData.value.tripType) {
    formData.value.tripType = newFormData.tripType;
  }
  
  if (newFormData.date !== formData.value.date) {
    formData.value.date = newFormData.date;
  }
  
  if (newFormData.time !== formData.value.time) {
    formData.value.time = newFormData.time;
  }
}

const handleWaypoint = () =>  {
  router.push({ 
    path: '/transit-point',
    query: {
      mode: 'waypoint'  
    }
  });
}

const handleDeparture = () => {
  router.push({ 
    path: '/target-location',
    query: {
      mode: 'departure'  
    }
  });
}

const handleDestinationMapClick = () => {
  router.push({ 
    path: '/target-stop-location',
    query: {
      mode: 'destination'  
    }
  });
}

// 更新位置信息的通用函数
const updateLocationData = (
  location: LocationData | null,
  isDestination = false
) => {
  if (isUpdatingForm) return;
  
  const locationTypeStr = isDestination ? '目的地' : '始发地';
  
  if (!location || !location.name) {
    if (isDestination) {
      Object.assign(formData.value, {
        destination: '',
        destinationId: '',
        destinationType: ''
      });
    } else {
      Object.assign(formData.value, {
        departure: '',
        departureId: '',
        departureType: ''
      });
      selectedLocations.value.departure = null;
    }
    return;
  }

  const detectedType = LocationTypeDetector.detectLocationType(location);
  
  if (isDestination) {
    Object.assign(formData.value, {
      destination: location.name,
      destinationId: location.id?.toString() || '',
      destinationType: detectedType
    });
  } else {
    Object.assign(formData.value, {
      departure: location.name,
      departureId: location.id?.toString() || '',
      departureType: detectedType
    });

    if (detectedType && detectedType !== '') {
      selectedLocations.value.departure = {
        name: location.name,
        id: location.id?.toString() || '',
        type: detectedType as 'prefecture' | 'area' | 'busStop',
        coordinates: location.coordinates || { lat: 0, lng: 0 },
        parentId: location.parentId,
        level: location.level,
        hasChildren: location.hasChildren,
        children: location.children,
        busStopInfo: location.busStopInfo
      };
    }
  }
}

// 监听器 - 移除深度监听
watchEffect(() => {
  const spot = selectedSpot.value;
  updateLocationData(spot, true);
});

watchEffect(() => {
  const originData = originStore.currentOriginData;
  updateLocationData(originData, false);
});


onMounted(() => {
  
  if (route.query.touristData) {
    try {
      touristData.value = JSON.parse(route.query.touristData as string);
    } catch (error) {
      console.error('❌ 路由数据解析失败:', error);
    }
  }
  
  // 延迟初始化，确保所有组件都已挂载
  nextTick(() => {
    syncWaypointsToForm();
  });
})

onUnmounted(() => {
  isUpdatingForm = true;
});
</script>

<template>
  <div>
    <!-- 标题栏 -->
    <div class="regionHeaderTop createPad">
      <button class="regionSelectorBackBtn" @click="handleBack">
        <svg width="16" height="16" viewBox="0 0 16 16">
          <path d="M10 4L6 8l4 4" stroke="#26499D" stroke-width="1.5" fill="none" />
        </svg>
      </button>
      <h3 class="regionSelectorTitle">ルート検索</h3>
    </div>
    
    <div class="round">
      <OneWayRoundTripRouteSelect 
        :departure-config="{
          value: formData.departure,
          readonly: true,
          showMapButton: false
        }" 
        :destination-config="{
          value: formData.destination,
          readonly: true,
          showMapButton: true
        }" 
        :waypoint-config="{
          readonly: true,
          showMapButton: false
        }" 
        :initial-form-data="formData" 
        @search="handleSearch" 
        @form-change="handleFormChange"
        @departure-click="handleDeparture" 
        @destination-click="handleDestinationMapClick" 
        @waypoint-click="handleWaypoint"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.round {
  padding: 20px;
}
</style>