<template>
  <div class="event-spot" >
    <h2 class="section-title">{{ sectionTitle }}</h2>
    <div class="title-underline"></div>

    <main class="attractions-container">

      <!-- Error state -->
      <div v-if="error" class="error-container">
        <p>データの読み込みに失敗しました</p>
      </div>

      <!-- Content -->
      <template v-else-if="events.length > 0">
        <!-- Desktop View -->
        <div class="attractions-grid desktop-view">
          <div
              v-for="event in events"
              :key="event.eventId"
              class="attraction-card"
              @click="navigateToDetail(event.eventId)"
          >
            <div class="card-image">
              <img
                  :src="event.image"
                  :alt="event.name"
                  class="attraction-image"
                  @error="handleImageError"
              />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ event.name }}</h3>
              <p class="card-description">{{ event.description }}</p>
            </div>
          </div>
        </div>

        <!-- Mobile Carousel View -->
        <div class="mobile-view">
          <div class="carousel-container">
            <EventSpotMobile
                :items="events"
                @item-click="navigateToDetail"
            />
          </div>
        </div>

        <!-- 更多按钮 -->
        <div class="more-btn">
          <v-btn @click="navigateToEventsList" variant="outlined" >
            もっと見る
          </v-btn>

        </div>

      </template>

      <!-- Empty state -->
      <div v-else class="empty-container">
        <p>現在開催中のイベントはありません</p>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from 'vue-router';
import EventSpotMobile from "./EventSpotMobile.vue";

const props = defineProps({
  sectionTitle: {
    type: String,
    default: "期間限定・イベント",
  },
  apiEndpoint: {
    type: String,
    default: "/event" // デフォルトのAPIエンドポイント
  },
  maxItems: {
    type: Number,
    default: 6 // 最大取得件数
  }
});

// Router for navigation
const router = useRouter();

// Reactive data
const events = ref([]);
const loading = ref(false);
const error = ref(null);
const totalCount = ref(0);


// API request function
const fetchEvents = async () => {
  loading.value = true;
  error.value = null;
  events.value = [
    {
      eventId: 101,
      name: "春の桜祭り",
      prefectureId: 13,
      address: "東京都千代田区皇居外苑",
      startDate: "2025-03-20",
      endDate: "2025-04-10",
      image: "https://picsum.photos/400/300?random=1",
      description: "春の桜祭り春の桜祭り"
    },
    {
      eventId: 102,
      name: "夏祭り花火大会",
      prefectureId: 13,
      address: "東京都墨田区隅田川河川敷",
      startDate: "2025-07-15",
      endDate: "2025-07-15",
      image: "https://picsum.photos/400/300?random=2",
      description: "夏祭り花火大会夏祭り花火大会"
    },
    {
      eventId: 103,
      name: "秋の紅葉ライトアップ",
      prefectureId: 14,
      address: "神奈川県鎌倉市長谷",
      startDate: "2025-11-01",
      endDate: "2025-12-15",
      image: "https://picsum.photos/400/300?random=3",
      description: "秋の紅葉ライトアップ秋の紅葉ライトアップ"
    },
    {
      eventId: 104,
      name: "冬のイルミネーション",
      prefectureId: 13,
      address: "東京都渋谷区表参道",
      startDate: "2025-12-01",
      endDate: "2025-12-31",
      image: "https://picsum.photos/400/300?random=4",
      description: "冬のイルミネーション冬のイルミネーション"
    },
    {
      eventId: 105,
      name: "伝統工芸品展示会",
      prefectureId: 27,
      address: "大阪府大阪市中央区難波",
      startDate: "2025-06-10",
      endDate: "2025-06-20",
      image: "https://picsum.photos/400/300?random=5",
      description: "伝統工芸品展示会伝統工芸品展示会"
    },
    {
      eventId: 106,
      name: "海の日フェスティバル",
      prefectureId: 14,
      address: "神奈川県江ノ島片瀬海岸",
      startDate: "2025-07-20",
      endDate: "2025-07-21",
      image: "https://picsum.photos/400/300?random=6",
      description: "海の日フェスティバル海の日フェスティバル"
    },
  ];
  try {
    // const response = await $fetch(props.apiEndpoint, {
    //   query: {
    //     limit: props.maxItems,
    //     // Add other query parameters as needed
    //   }
    // });
    //
    //
    // if (response && response.data && response.data.events) {
    //   events.value = response.data.events;
    //   totalCount.value = response.data.total_count || 0;
    // } else {
    //   throw new Error('Invalid response format');
    // }
  } catch (err) {
    console.error('Failed to fetch events:', err);
    error.value = err.message || 'Failed to fetch events';
    events.value = [];
  } finally {
    loading.value = false;
  }
};

// Navigate to event detail page
const navigateToDetail = (eventId) => {
  router.push(`/${props.apiEndpoint}/detail/${eventId}`);
};

// Navigate to events list page
const navigateToEventsList = () => {
  router.push(`/${props.apiEndpoint}`);
};

// Handle image loading error
const handleImageError = (event) => {
  event.target.src = "https://picsum.photos/400/200?grayscale&blur=2";
  event.target.alt = "画像が読み込めませんでした";
};

// Format date range for display
const formatDateRange = (startDate, endDate) => {
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  if (startDate === endDate) {
    return formatDate(startDate);
  }

  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
};

// Fetch events when component is mounted
onMounted(() => {
  fetchEvents();
});
</script>

<style scoped lang="scss">
$primary-color: #26499d;

// Loading styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Error styles
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  p {
    color: #e74c3c;
    font-size: 16px;
    margin-bottom: 16px;
  }

  .retry-btn {
    padding: 8px 16px;
    border: 1px solid $primary-color;
    background: #fff;
    color: $primary-color;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: $primary-color;
      color: #fff;
    }
  }
}

// Empty state styles
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;

  p {
    color: #999;
    font-size: 16px;
  }
}

.more-btn {
  text-align: center;
}

.section-title {
  text-align: center;
  color: $primary-color;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  padding-top: 20px;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: $primary-color;
  margin: 0 auto 20px;
}

.attractions-container {
  margin: 0 auto;
  padding: 0 20px 60px;
}

/* Desktop View */
.desktop-view {
  width: 61%;
  margin: 0 auto;
  padding: 20px;
  display: block;
}

.mobile-view {
  display: none;
}

.attractions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Switch to mobile view */
  .desktop-view {
    display: none;
  }

  .mobile-view {
    display: block;
  }

  .attractions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .attractions-container {
    padding: 0 15px 40px;
  }

  .card-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .attractions-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-image {
    height: 150px;
  }

  .card-title {
    font-size: 15px;
  }

  .card-description {
    font-size: 13px;
  }
}

.attraction-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  }
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.attraction-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.attraction-card:hover .attraction-image {
  transform: scale(1.05);
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.card-description {
  font-size: 12px;
  color: #666;
  line-height: 1;
  margin: 0 0 0 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-dates {
  .date-range {
    font-size: 12px;
    color: $primary-color;
    background: rgba($primary-color, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
  }
}

/* Carousel Styles */
.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 30px;
}
</style>