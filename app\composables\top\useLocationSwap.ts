// pages/top/composables/useLocationSwap.ts

export interface Waypoint {
  id: number
  location: string
}

export interface SwapResult {
  departure: string
  destination: string
  waypoints?: Waypoint[]
}

export interface LocationSwap {
  swapLocations: (departure: string, destination: string, waypoints?: Waypoint[]) => SwapResult
  swapSimpleLocations: (departure: string, destination: string) => SwapResult
  swapLocationsWithWaypoints: (departure: string, destination: string, waypoints: Waypoint[]) => SwapResult
}

export function useLocationSwap(): LocationSwap {
  // 简单的位置交换（无经由地）
  const swapSimpleLocations = (departure: string, destination: string): SwapResult => {
    return {
      departure: destination,
      destination: departure
    }
  }

  // 带经由地的位置交换
  const swapLocationsWithWaypoints = (
    departure: string, 
    destination: string, 
    waypoints: Waypoint[]
  ): SwapResult => {
    // 保存所有位置
    const allLocations = [departure, ...waypoints.map(w => w.location), destination]
    
    // 反转所有位置的顺序
    allLocations.reverse()
    
    // 重新分配位置
    const newDeparture = allLocations[0]
    const newDestination = allLocations[allLocations.length - 1]
    const newWaypoints = waypoints.map((waypoint, index) => ({
      ...waypoint,
      location: allLocations[index + 1]
    }))

    return {
      departure: newDeparture,
      destination: newDestination,
      waypoints: newWaypoints
    }
  }

  // 通用位置交换函数
  const swapLocations = (
    departure: string, 
    destination: string, 
    waypoints: Waypoint[] = []
  ): SwapResult => {
    if (waypoints.length > 0) {
      return swapLocationsWithWaypoints(departure, destination, waypoints)
    } else {
      return swapSimpleLocations(departure, destination)
    }
  }

  return {
    swapLocations,
    swapSimpleLocations,
    swapLocationsWithWaypoints
  }
}