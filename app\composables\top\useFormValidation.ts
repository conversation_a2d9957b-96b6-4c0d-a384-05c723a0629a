// pages/top/composables/useFormValidation.ts
import { ref } from 'vue'

export interface ValidationResult {
  valid: boolean
}

export interface FormValidation {
  formRef: any
  validationRules: Record<string, Array<(v: string) => string | boolean>>
  validateForm: () => Promise<ValidationResult>
  resetValidation: () => void
}

export function useFormValidation(): FormValidation {
  const formRef = ref<any>(null)

  // 通用验证规则
  const validationRules = {
    departure: [(v: string) => !!v || '出発地を選択してください'],
    destination: [(v: string) => !!v || '到着地を選択してください'],
    date: [(v: string) => !!v || '日付を選択してください'],
    time: [(v: string) => !!v || '時間を選択してください'],
    outboundDate: [(v: string) => !!v || '行きの日付を選択してください'],
    outboundTime: [(v: string) => !!v || '行きの時間を選択してください'],
    returnDate: [(v: string) => !!v || '帰りの日付を選択してください'],
    returnTime: [(v: string) => !!v || '帰りの時間を選択してください'],
    email: [
      (v: string) => !!v || 'メールアドレスを入力してください',
      (v: string) => {
        const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return pattern.test(v) || '有効なメールアドレスを入力してください'
      }
    ]
  }

  // 验证表单
  const validateForm = async (): Promise<ValidationResult> => {
    if (!formRef.value) return { valid: false }
    return await formRef.value.validate()
  }

  // 重置验证状态
  const resetValidation = (): void => {
    if (formRef.value) {
      formRef.value.resetValidation()
    }
  }

  return {
    formRef,
    validationRules,
    validateForm,
    resetValidation
  }
}