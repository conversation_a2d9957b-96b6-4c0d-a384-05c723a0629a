<!-- TimePicker.vue -->
<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'

// Props - 接收当前选中的时间值
const props = defineProps({
  selectedTimeValue: {
    type: String,
    default: '指定なし'
  }
})

// Emits - 移除了原有的内部状态管理，只作为弹窗组件
const emit = defineEmits(['timeSelected', 'close'])

/* 状态 */
const currentTab = ref('none') // 默认为'指定なし'

const visibleItemsCount = 5
const middlePosition = Math.floor(visibleItemsCount / 2)

/* 数据 */
const hourList = ref(
  Array.from({ length: 25 }, (_, i) => `${i}`.padStart(2, '0'))
)
const minuteList = ref(
  Array.from({ length: 12 }, (_, i) => `${i * 5}`.padStart(2, '0'))
)

const hourActiveIndex = ref(12) // 默认12点
const minuteActiveIndex = ref(6) // 默认30分

/* transform 偏移量（应用到整列容器）*/
const hourTranslate = ref(0)
const minuteTranslate = ref(0)

/* 常量：行高（务必与 CSS 保持一致） */
const rowHeight = 34

/* 小工具 */
function clamp(v, a, b) {
  return Math.max(a, Math.min(b, v))
}

/* 根据 activeIndex 计算 translate（把 active 项精确放中间） */
function syncScrollPosition(column) {
  if (column === 'hour') {
    hourTranslate.value = (middlePosition - hourActiveIndex.value) * rowHeight
  } else {
    minuteTranslate.value =
      (middlePosition - minuteActiveIndex.value) * rowHeight
  }
}

/* 对齐并同步 translate */
function snapToNearestIndex(column) {
  if (column === 'hour') {
    hourActiveIndex.value = Math.round(hourActiveIndex.value)
  } else {
    minuteActiveIndex.value = Math.round(minuteActiveIndex.value)
  }
  syncScrollPosition(column)
}

// 解析时间字符串
const parseTimeValue = (timeString) => {
  // 确保timeString是字符串类型
  if (
    !timeString ||
    typeof timeString !== 'string' ||
    timeString === '指定なし'
  ) {
    return {
      tab: 'none',
      hour: 12,
      minute: 6 // 30分对应minuteList的索引6 (6 * 5 = 30)
    }
  }

  // 解析时间格式 HH:mm
  const timeParts = timeString.split(':')
  if (timeParts.length === 2) {
    const hour = parseInt(timeParts[0])
    const minute = parseInt(timeParts[1])

    // 验证小时和分钟的有效性
    if (
      !isNaN(hour) &&
      !isNaN(minute) &&
      hour >= 0 &&
      hour <= 24 &&
      minute >= 0 &&
      minute <= 59
    ) {
      // 找到分钟在minuteList中最接近的索引（每5分钟一个选项）
      const minuteIndex = Math.round(minute / 5)
      const validMinuteIndex = Math.min(
        minuteIndex,
        minuteList.value.length - 1
      )

      return {
        tab: 'timeSpecified',
        hour: hour,
        minute: validMinuteIndex
      }
    }
  }

  // 如果解析失败，返回默认值
  return {
    tab: 'none',
    hour: 12,
    minute: 6
  }
}

// 初始化时间值
const initializeTimeValue = () => {
  const parsed = parseTimeValue(props.selectedTimeValue)
  currentTab.value = parsed.tab
  hourActiveIndex.value = parsed.hour
  minuteActiveIndex.value = parsed.minute

  // 同步滚动位置
  syncScrollPosition('hour')
  syncScrollPosition('minute')
}

/* 拖拽状态 */
const touchState = ref({
  isDragging: false,
  startY: 0,
  startIndex: 0, // 拖拽起点（以 activeIndex 为起点）
  column: '',
  lastY: 0,
  lastTime: 0,
  velocity: 0
})

/* document 级别的 mouse move/up 处理器（用于 mouse 拖拽） */
const mouseMoveListener = (e) => {
  handleTouchMove(e, touchState.value.column)
}
const mouseUpListener = (e) => {
  handleTouchEnd(touchState.value.column)
}

/* wheel（PC） */
const handleWheel = (e, column) => {
  e.stopPropagation()
  const delta = e.deltaY > 0 ? 1 : -1
  if (column === 'hour') {
    hourActiveIndex.value = clamp(
      hourActiveIndex.value + delta,
      0,
      hourList.value.length - 1
    )
  } else {
    minuteActiveIndex.value = clamp(
      minuteActiveIndex.value + delta,
      0,
      minuteList.value.length - 1
    )
  }
  syncScrollPosition(column)
}

/* touch/mouse start */
function handleTouchStart(e, column) {
  // 获取 Y 坐标（触控或鼠标）
  const y = e.touches ? e.touches[0].clientY : e.clientY
  touchState.value.isDragging = true
  touchState.value.startY = y
  touchState.value.lastY = y
  touchState.value.lastTime = Date.now()
  touchState.value.velocity = 0
  touchState.value.column = column

  // **关键修复**：以当前 activeIndex 作为起始索引（而不是点到的那个项 index）
  touchState.value.startIndex =
    column === 'hour' ? hourActiveIndex.value : minuteActiveIndex.value

  // 如果是鼠标则在 document 级别捕获 move/up
  if (!e.touches) {
    document.addEventListener('mousemove', mouseMoveListener)
    document.addEventListener('mouseup', mouseUpListener)
  }
}

/* touch/mouse move */
function handleTouchMove(e, column) {
  if (!touchState.value.isDragging || touchState.value.column !== column) return

  const y = e.touches ? e.touches[0].clientY : e.clientY
  const now = Date.now()
  const dt = now - touchState.value.lastTime

  if (dt > 0) {
    // 当前速度 px/ms
    touchState.value.velocity = (y - touchState.value.lastY) / dt
  }
  touchState.value.lastY = y
  touchState.value.lastTime = now

  const totalDiffY = y - touchState.value.startY
  const diffIndex = Math.round(totalDiffY / rowHeight)
  // startIndex - diffIndex 保持和视觉方向一致（向上滑选 index 增/减根据你的期望）
  const newIndex = touchState.value.startIndex - diffIndex

  if (column === 'hour') {
    hourActiveIndex.value = clamp(newIndex, 0, hourList.value.length - 1)
  } else {
    minuteActiveIndex.value = clamp(newIndex, 0, minuteList.value.length - 1)
  }
  syncScrollPosition(column)
}

/* touch/mouse end（含一个简单惯性实现） */
function handleTouchEnd(column) {
  if (touchState.value.column !== column) return

  // 简单惯性：根据最后的速度给额外移动若干格（可调）
  const velocity = touchState.value.velocity || 0
  const inertiaMultiplier = 20 // 速度->格数的转换值（可调）
  const maxExtra = 3 // 最大附加格数（可调）

  let extra = Math.round(velocity * inertiaMultiplier)
  extra = clamp(extra, -maxExtra, maxExtra)

  if (column === 'hour') {
    hourActiveIndex.value = clamp(
      hourActiveIndex.value - extra,
      0,
      hourList.value.length - 1
    )
  } else {
    minuteActiveIndex.value = clamp(
      minuteActiveIndex.value - extra,
      0,
      minuteList.value.length - 1
    )
  }

  // 对齐到最近整数索引并同步位置
  snapToNearestIndex(column)

  touchState.value.isDragging = false
  touchState.value.column = ''

  // 移除 document 级别监听（鼠标）
  document.removeEventListener('mousemove', mouseMoveListener)
  document.removeEventListener('mouseup', mouseUpListener)
}

/* 按钮回调 */
const handleTabChange = (tab) => {
  currentTab.value = tab
}

const cancel = () => {
  emit('close')
}

const confirm = () => {
  // 根据当前标签返回相应的时间值
  let timeValue
  if (currentTab.value === 'timeSpecified') {
    timeValue = `${hourList.value[hourActiveIndex.value]}:${
      minuteList.value[minuteActiveIndex.value]
    }`
  } else {
    timeValue = '指定なし'
  }

  emit('timeSelected', timeValue)
  emit('close')
}

/* 全局阻止背景滚动（兼容性） */
const preventBackgroundScroll = (e) => {
  e.preventDefault()
}

// 监听props变化，更新时间值
watch(
  () => props.selectedTimeValue,
  (newValue) => {
    initializeTimeValue()
  },
  { immediate: true }
)

onMounted(() => {
  // 阻止背景滚动（touch）
  document.addEventListener('touchmove', preventBackgroundScroll, {
    passive: false
  })
})

onBeforeUnmount(() => {
  document.removeEventListener('touchmove', preventBackgroundScroll)
  document.removeEventListener('mousemove', mouseMoveListener)
  document.removeEventListener('mouseup', mouseUpListener)
})
</script>

<template>
  <div class="popup-overlay">
    <div class="popup-content">
      <div class="popup-header">
        <span class="title">時間指定</span>
        <button class="close-btn" @click="cancel">×</button>
      </div>

      <div class="tabs">
        <button
          class="tab"
          :class="{ active: currentTab === 'timeSpecified' }"
          @click="handleTabChange('timeSpecified')"
        >
          時間指定
        </button>
        <button
          class="tab"
          :class="{ active: currentTab === 'none' }"
          @click="handleTabChange('none')"
        >
          指定なし
        </button>
      </div>

      <div v-if="currentTab === 'timeSpecified'" class="time-picker-container">
        <!-- 中间灰色选中遮罩 -->
        <div class="selected-time-mask"></div>

        <div class="time-picker">
          <!-- 小时列 -->
          <div
            class="picker-column"
            @wheel.prevent="handleWheel($event, 'hour')"
          >
            <div
              class="picker-items"
              :style="{ transform: `translateY(${hourTranslate}px)` }"
            >
              <div
                v-for="(hour, index) in hourList"
                :key="`h-${index}`"
                :class="{ active: index === hourActiveIndex }"
                @touchstart="handleTouchStart($event, 'hour')"
                @touchmove.prevent="handleTouchMove($event, 'hour')"
                @touchend="handleTouchEnd('hour')"
                @mousedown.prevent="handleTouchStart($event, 'hour')"
              >
                {{ hour }}
              </div>
            </div>
          </div>

          <div class="separator">:</div>

          <!-- 分钟列 -->
          <div
            class="picker-column"
            @wheel.prevent="handleWheel($event, 'minute')"
          >
            <div
              class="picker-items"
              :style="{ transform: `translateY(${minuteTranslate}px)` }"
            >
              <div
                v-for="(minute, index) in minuteList"
                :key="`m-${index}`"
                :class="{ active: index === minuteActiveIndex }"
                @touchstart="handleTouchStart($event, 'minute')"
                @touchmove.prevent="handleTouchMove($event, 'minute')"
                @touchend="handleTouchEnd('minute')"
                @mousedown.prevent="handleTouchStart($event, 'minute')"
              >
                {{ minute }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else>
        <div class="none-state">
          <div>--</div>
          <div>--</div>
        </div>
      </div>

      <div class="buttons">
        <button class="cancel-btn" @click="cancel">キャンセル</button>
        <button class="confirm-btn" @click="confirm">決定</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.none-state {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ebebeb;
  margin: 80px 20px;
  border-radius: 6px;
  height: 34px;
}

.popup-overlay {
  background: #fff;
  border-radius: 8px;
  width: 300px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.popup-content {
  background: #fff;
  border-radius: 8px;
  width: 100%;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #26499d;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 auto;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.tabs {
  display: flex;
  padding: 10px;
}

.tab {
  flex: 1;
  padding: 6px 0;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin: 0 5px;
  cursor: pointer;
  background: #fff;
}

.tab.active {
  background: #acd1ed;
  color: #000;
  font-weight: bold;
}

.time-picker-container {
  position: relative;
  padding: 15px 0;
  height: 205px;
}

/* 5 * 34 =170 */
.selected-time-mask {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 258px;
  height: 34px;
  background: rgba(204, 204, 204, 0.5);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  pointer-events: none;
  border-radius: 6px;
}

.time-picker {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  z-index: 1;
}

.picker-column {
  width: 80px;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
  position: relative;
}

.picker-items {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.15s cubic-bezier(0.2, 0.9, 0.3, 1);
}

.picker-items > div {
  box-sizing: border-box;
  width: 100%;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.picker-items > div.active {
  font-weight: 700;
  color: #000;
  font-size: 16px;
  transform: scale(1.05);
}

.picker-items > div:not(.active) {
  opacity: 0.8;
  transform: scale(0.95);
}

.separator {
  width: 20px;
  text-align: center;
  font-size: 20px;
  color: #666;
  font-weight: bold;
}

.buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #eee;
}

.cancel-btn,
.confirm-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background: #e7f2fa;
  border: 1px solid #9cbcd4;
  margin-right: 10px;
  color: #26499d;
  width: 131px;
}

.confirm-btn {
  width: 205px;
  background: #26499d;
  color: #fff;
  border: 1px solid #007bff;
}
</style>
