<script setup lang="ts">
import { ref, computed, onMounted, nextTick, onUnmounted, watch } from 'vue';
import SearchResult from '../../components/OriginDestHistoryInput.vue'
import { isLogin } from '~/utils/auth'
import {useWaypointStore } from '~/stores/edit';
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 初始化pinia store
const waypointStore = useWaypointStore();

const isLoggedIn = computed(() => !isLogin())

definePageMeta({
  hideFooter: true
});

// 接口定义
interface BusStop {
  bus_stop_id: number;
  bus_stop_name: string;
  bus_stop_short_name: string;
  latitude: number;
  longitude: number;
  has_toilet: boolean;
  has_wifi: boolean;
  labels: string[];
}

interface Area {
  area_id: number;
  area_name: string;
  display_order: number;
  latitude: number;
  longitude: number;
  bus_stops?: BusStop[];
}

interface Prefecture {
  prefecture_id: number;
  prefecture_name: string;
  display_order: number;
  latitude: number;
  longitude: number;
  areas: Area[];
}

interface Region {
  region_id: number;
  region_name: string;
  display_order: number;
  prefectures?: Prefecture[];
}

interface DataItem {
  id: number;
  name: string;
  type: string;
}

interface SelectedNode {
  id: number;
  name: string;
  type: 'prefecture' | 'area' | 'bus_stop';
  level: 1 | 2 | 3;
}

interface TouristSpot {
  id: number;
  name: string;
  [key: string]: any;
}

// JSON数据
const jsonData = {
  data: [
    {
      region_id: 1,
      region_name: "関東",
      display_order: 1,
      prefectures: [
        {
          prefecture_id: 13,
          prefecture_name: "東京都",
          display_order: 1,
          latitude: 35.6895,
          longitude: 139.6917,
          areas: [
            {
              area_id: 1,
              area_name: "新宿区",
              display_order: 1,
              latitude: 35.6938,
              longitude: 139.7034,
              bus_stops: [
                {
                  bus_stop_id: 1,
                  bus_stop_name: "新宿駅西口",
                  bus_stop_short_name: "新宿西口",
                  latitude: 35.6909,
                  longitude: 139.6995,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ["map_device"]
                },
                {
                  bus_stop_id: 2,
                  bus_stop_name: "新宿三丁目",
                  bus_stop_short_name: "新宿3丁目",
                  latitude: 35.6901,
                  longitude: 139.7052,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            },
            {
              area_id: 2,
              area_name: "渋谷区",
              display_order: 2,
              latitude: 35.6617,
              longitude: 139.7040,
              bus_stops: [
                {
                  bus_stop_id: 3,
                  bus_stop_name: "渋谷駅東口",
                  bus_stop_short_name: "渋谷東口",
                  latitude: 35.6580,
                  longitude: 139.7016,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ["map"]
                }
              ]
            }
          ]
        },
        {
          prefecture_id: 14,
          prefecture_name: "神奈川県",
          display_order: 2,
          latitude: 35.4478,
          longitude: 139.6425,
          areas: [
            {
              area_id: 3,
              area_name: "横浜市",
              display_order: 1,
              latitude: 35.4437,
              longitude: 139.6380,
              bus_stops: [
                {
                  bus_stop_id: 4,
                  bus_stop_name: "横浜駅西口",
                  bus_stop_short_name: "横浜西口",
                  latitude: 35.4658,
                  longitude: 139.6201,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ["map_device"]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

const regionData: Region[] = [
  {
    region_id: 1,
    region_name: "予約履歴から",
    display_order: 1,
    prefectures: [
      {
        prefecture_id: 1,
        prefecture_name: "北海道",
        display_order: 1,
        latitude: 43.0642,
        longitude: 141.3469,
        areas: []
      }
    ]
  },
  {
    region_id: 2,
    region_name: "お気に入り停留所",
    display_order: 2,
    prefectures: [
      {
        prefecture_id: 2,
        prefecture_name: "青森県",
        display_order: 1,
        latitude: 40.8244,
        longitude: 140.74,
        areas: [
          {
            area_id: 3,
            area_name: "青森市",
            display_order: 1,
            latitude: 40.8244,
            longitude: 140.74,
          }
        ]
      }
    ]
  }
];

const dataList: DataItem[] = [
  { id: 1, name: '东京', type: '东京都' },
  { id: 2, name: '大阪', type: '大阪府' },
];

// 响应式数据
const searchValue = ref('');
const searchResults = ref<DataItem[]>([]);
const activeTag = ref<string>('');
const expandedRegions = ref<Set<string>>(new Set());
const selectedNode = ref<SelectedNode | null>(null);
const selectedItems = ref<Set<string>>(new Set());

// 关键修复：防重复操作状态
const isProcessing = ref(false);
const isNavigating = ref(false);
const confirmClickCount = ref(0);

// 计算属性
const regionTags = computed(() => {
  return jsonData.data.map(region => region.region_name);
});

const currentRegionData = computed<Region | undefined>(() => {
  return jsonData.data.find(region => region.region_name === activeTag.value);
});

const mode = computed(() => route.query.mode || 'departure');

const confirmButtonText = computed(() => {
  if (!selectedNode.value) {
    return '到着地選択に進む';
  }
  return `決定（${selectedNode.value.name}）`;
});

// 搜索处理
const handleSearch = () => {
  if (searchValue.value.trim() === '') {
    searchResults.value = [];
    return;
  }
  searchResults.value = dataList.filter(item =>
    item.name.includes(searchValue.value)
  );
};

// 标签设置
function setActiveTag(tag: string): void {
  if (isProcessing.value || isNavigating.value) return;
  
  activeTag.value = tag;
  expandedRegions.value.clear();
  selectedNode.value = null;
  selectedItems.value.clear();
}

// 地区展开/折叠
function toggleRegion(regionId: string): void {
  if (isProcessing.value || isNavigating.value) return;
  
  if (expandedRegions.value.has(regionId)) {
    expandedRegions.value.delete(regionId);
  } else {
    expandedRegions.value.add(regionId);
  }
}

// 获取所有子节点ID
function getAllChildIdsForPrefecture(prefecture: Prefecture): string[] {
  const childIds: string[] = [];
  
  prefecture.areas.forEach(area => {
    childIds.push(`area_${area.area_id}`);
    area.bus_stops?.forEach(busStop => {
      childIds.push(`bus_${busStop.bus_stop_id}`);
    });
  });
  
  return childIds;
}
// 选中状态检查函数
function isNodeSelected(id: string, type: 'prefecture' | 'area' | 'bus_stop', nodeId: number): boolean {
  if (!selectedNode.value) return false;
  return selectedNode.value.type === type && selectedNode.value.id === nodeId;
}

function isPrefectureFullySelected(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false;
  return selectedNode.value.type === 'prefecture' && selectedNode.value.id === prefecture.prefecture_id;
}

function isAreaFullySelected(area: Area): boolean {
  if (!selectedNode.value) return false;
  return selectedNode.value.type === 'area' && selectedNode.value.id === area.area_id;
}

function hasPartialSelection(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false;
  
  if (selectedNode.value.type === 'area') {
    return prefecture.areas.some(area => area.area_id === selectedNode.value!.id);
  }
  
  if (selectedNode.value.type === 'bus_stop') {
    return prefecture.areas.some(area => 
      area.bus_stops?.some(bus => bus.bus_stop_id === selectedNode.value!.id)
    );
  }
  
  return false;
}

function hasPartialSelectionArea(area: Area): boolean {
  if (!selectedNode.value) return false;
  
  if (selectedNode.value.type === 'bus_stop') {
    return area.bus_stops?.some(bus => bus.bus_stop_id === selectedNode.value!.id) || false;
  }
  
  return false;
}

// 选择处理函数 - 添加防重复检查
function handlePrefectureSelection(prefecture: Prefecture, event: Event): void {
  if (isProcessing.value || isNavigating.value) return;
  
  const target = event.target as HTMLInputElement;
  
  
  selectedNode.value = null;
  selectedItems.value.clear();
  
  if (target.checked) {
    selectedNode.value = {
      id: prefecture.prefecture_id,
      name: prefecture.prefecture_name,
      type: 'prefecture',
      level: 1
    };
    
    const allChildIds = getAllChildIdsForPrefecture(prefecture);
    allChildIds.forEach(id => selectedItems.value.add(id));
    
  }
}

function handleAreaSelection(area: Area, event: Event): void {
  if (isProcessing.value || isNavigating.value) return;
  
  const target = event.target as HTMLInputElement;
  
  
  selectedNode.value = null;
  selectedItems.value.clear();
  
  if (target.checked) {
    selectedNode.value = {
      id: area.area_id,
      name: area.area_name,
      type: 'area',
      level: 2
    };
    
    area.bus_stops?.forEach(busStop => {
      selectedItems.value.add(`bus_${busStop.bus_stop_id}`);
    });
    selectedItems.value.add(`area_${area.area_id}`);
    
  }
}

function handleBusStopSelection(busStop: BusStop, event: Event): void {
  if (isProcessing.value || isNavigating.value) return;
  
  const target = event.target as HTMLInputElement;
  
  selectedNode.value = null;
  selectedItems.value.clear();
  
  if (target.checked) {
    selectedNode.value = {
      id: busStop.bus_stop_id,
      name: busStop.bus_stop_name,
      type: 'bus_stop',
      level: 3
    };
    selectedItems.value.add(`bus_${busStop.bus_stop_id}`);
  }
}

// 登录状态下的选择处理函数
function isLoggedInPrefectureSelected(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false;
  return selectedNode.value.type === 'prefecture' && selectedNode.value.id === prefecture.prefecture_id;
}

function isLoggedInAreaSelected(area: Area): boolean {
  if (!selectedNode.value) return false;
  return selectedNode.value.type === 'area' && selectedNode.value.id === area.area_id;
}

function hasLoggedInPartialSelection(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false;
  
  if (selectedNode.value.type === 'area') {
    return prefecture.areas.some(area => area.area_id === selectedNode.value!.id);
  }
  
  return false;
}

function handleLoggedInPrefectureSelection(prefecture: Prefecture, event: Event): void {
  if (isProcessing.value || isNavigating.value) return;
  
  const target = event.target as HTMLInputElement;
  
  selectedNode.value = null;
  selectedItems.value.clear();
  
  if (target.checked) {
    selectedNode.value = {
      id: prefecture.prefecture_id,
      name: prefecture.prefecture_name,
      type: 'prefecture',
      level: 1
    };
    
    prefecture.areas.forEach(area => {
      selectedItems.value.add(`area_${area.area_id}`);
    });
  }
}

function handleLoggedInAreaSelection(area: Area, event: Event): void {
  if (isProcessing.value || isNavigating.value) return;
  
  const target = event.target as HTMLInputElement;
  
  selectedNode.value = null;
  selectedItems.value.clear();
  
  if (target.checked) {
    selectedNode.value = {
      id: area.area_id,
      name: area.area_name,
      type: 'area',
      level: 2
    };
    selectedItems.value.add(`area_${area.area_id}`);
  }
}

// 返回按钮处理函数
function handleBack() {
  if (isNavigating.value) {
    return;
  }
  
  isNavigating.value = true;
  
  // 清理状态
  selectedNode.value = null;
  selectedItems.value.clear();
  expandedRegions.value.clear();
  
  router.go(-1);
}

// 获取标签显示文本
function getTagText(labels: string[]): string {
  return labels.join(', ');
}

// 核心修复：完全重写确认处理函数
const handleConfirm = async () => {
  // 记录点击次数用于调试
  confirmClickCount.value++;
  
  // 严格防重复点击检查
  if (isProcessing.value) {
    return;
  }
  
  if (isNavigating.value) {
    return;
  }

  if (!selectedNode.value) {
    return;
  }


  // 立即设置处理状态，防止重复操作
  isProcessing.value = true;

  try {
    const selectedData: TouristSpot = {
      id: selectedNode.value.id,
      name: selectedNode.value.name,
    };


    // 根据模式处理数据
    if (mode.value === 'waypoint') {
      
      // 检查当前经由地数量
      // const currentCount = waypointStore.getWaypointCount;
      
      // 添加经由地
      waypointStore.addWaypoint(selectedData);
      
      // 等待状态更新
      await nextTick();
      
      // 验证添加结果
      // const newCount = waypointStore.getWaypointCount;
    
    }

    // 等待所有状态更新完成
    await nextTick();
    await new Promise(resolve => setTimeout(resolve, 50));
    
    
    // 设置导航状态
    isNavigating.value = true;
    
    // 清理选择状态
    selectedNode.value = null;
    selectedItems.value.clear();
    
    
    // 延迟导航，确保所有状态更新完成
    setTimeout(() => {
      router.go(-1);
    }, 100);

  } catch (error) {
    console.error('处理确认操作时发生错误:', error);
    
    // 发生错误时重置状态
    isProcessing.value = false;
    isNavigating.value = false;
    
    // 用户友好的错误提示
    alert('操作失败，请重试');
  }
};



// 生命周期钩子
onMounted(() => {
  // 设置默认选中第一个标签
  if (regionTags.value.length > 0) {
    activeTag.value = regionTags.value[0];
  }
  
  // 重置状态
  isProcessing.value = false;
  isNavigating.value = false;
  confirmClickCount.value = 0;
  
 
});

onUnmounted(() => {
  // 清理状态
  isProcessing.value = false;
  isNavigating.value = false;
});

// 监听路由变化
watch(() => route.fullPath, (newPath, oldPath) => {
  if (newPath !== oldPath) {
    isProcessing.value = false;
    isNavigating.value = false;
  }
});



</script>
<template>
  <div class="regionSelector">
    <!-- 头部区域切换 -->
    <div class="regionSelectorHeader">
      <!-- 返回按钮和标题 -->
      <div class="regionSelectorHeaderTop">
        <button class="regionSelectorBackBtn" @click="handleBack">
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M10 4L6 8l4 4" stroke="#26499D" stroke-width="1.5" fill="none" />
          </svg>
        </button>
        <h3 class="regionSelectorTitle">経由地</h3>
      </div>

      <!-- 搜索框 -->
      <div>
        <v-text-field prepend-inner-icon="mdi-magnify" color="grey" v-model="searchValue" placeholder="出発地を入力"
          @input="handleSearch">
        </v-text-field>

        <SearchResult :searchResults="searchResults" />
      </div>

      <!-- 已登录的状态 使用regionData数组 -->
      <div v-if="isLoggedIn" class="regionSelectorList loggedInRegionList">
        <!-- 一级：地区层级 -->
        <div v-for="region in regionData" :key="region.region_id" class="regionSelectorItems">
          <div class="regionSelectorItemHeader" @click="toggleRegion(`loggedIn_region_${region.region_id}`)">
            <label class="regionSelectorCheckbox">
              <span class="regionSelectorLabels">{{ region.region_name }}</span>
            </label>
            <button
              :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`loggedIn_region_${region.region_id}`) }]"
              @click.stop="toggleRegion(`loggedIn_region_${region.region_id}`)">
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
              </svg>
            </button>
          </div>

          <!-- 二级：都道府县层级 -->
          <div v-if="expandedRegions.has(`loggedIn_region_${region.region_id}`)" class="regionSelectorChildren">
            <div v-for="prefecture in region.prefectures" :key="prefecture.prefecture_id"
              class="regionSelectorChildItem"
              :class="{ 'regionSelectorChildItemSelected': isLoggedInPrefectureSelected(prefecture) }">
              <div class="regionSelectorChildHeader" 
                @click="toggleRegion(`loggedIn_prefecture_${prefecture.prefecture_id}`)">
                <label class="regionSelectorCheckbox">
                  <input 
                    type="checkbox" 
                    :checked="isLoggedInPrefectureSelected(prefecture)"
                    :indeterminate="hasLoggedInPartialSelection(prefecture)"
                    @click.stop
                    @change="handleLoggedInPrefectureSelection(prefecture, $event)" />
                  <span class="regionSelectorLabels">{{ prefecture.prefecture_name }}</span>
                </label>
                <button
                  v-if="prefecture.areas && prefecture.areas.length > 0" 
                  :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`loggedIn_prefecture_${prefecture.prefecture_id}`) }]"
                  @click.stop="toggleRegion(`loggedIn_prefecture_${prefecture.prefecture_id}`)">
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
                  </svg>
                </button>
                <div v-else style="padding: 16px 16px 16px 32px;"></div>
              </div>

              <!-- 三级：区域层级 -->
              <div v-if="expandedRegions.has(`loggedIn_prefecture_${prefecture.prefecture_id}`)"
                class="regionSelectorSubChildren">
                <div v-for="area in prefecture.areas" :key="area.area_id" 
                  class="regionSelectorSubChildItem"
                  :class="{ 'regionSelectorSubChildItemSelected': isLoggedInAreaSelected(area) }">
                  <label class="regionSelectorCheckbox">
                    <input 
                      type="checkbox" 
                      :checked="isLoggedInAreaSelected(area)"
                      @change="handleLoggedInAreaSelection(area, $event)" />
                    <span class="regionSelectorLabels">{{ area.area_name }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 地区标签 -->
      <div class="regionSelectorTags">
        <button v-for="tag in regionTags" :key="tag"
          :class="['regionSelectorTag', { 'regionSelectorTagActive': activeTag === tag }]" @click="setActiveTag(tag)">
          {{ tag }}
        </button>
      </div>
    </div>

    <!-- 地区列表 -->
    <div class="regionSelectorContent">
      <div class="regionSelectorList" v-if="currentRegionData">
        <!-- 都道府县层级 -->
        <div v-for="prefecture in currentRegionData.prefectures" :key="prefecture.prefecture_id"
          class="regionSelectorItem"
          :class="{ 'regionSelectorItemSelected': isPrefectureFullySelected(prefecture) }">
          <div class="regionSelectorItemHeader" @click="toggleRegion(`prefecture_${prefecture.prefecture_id}`)">
            <label class="regionSelectorCheckbox">
              <input 
                type="checkbox" 
                :checked="isPrefectureFullySelected(prefecture)"
                :indeterminate="hasPartialSelection(prefecture)" 
                @click.stop
                @change="handlePrefectureSelection(prefecture, $event)" />
              <span class="regionSelectorCheckmark"></span>
              <span class="regionSelectorLabel">{{ prefecture.prefecture_name }}</span>
            </label>
            <button
              :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`prefecture_${prefecture.prefecture_id}`) }]"
              @click.stop="toggleRegion(`prefecture_${prefecture.prefecture_id}`)">
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
              </svg>
            </button>
          </div>

          <!-- 区域层级 -->
          <div v-if="expandedRegions.has(`prefecture_${prefecture.prefecture_id}`)" class="regionSelectorChildren">
            <div v-for="area in prefecture.areas" :key="area.area_id" class="regionSelectorChildItem"
              :class="{ 'regionSelectorChildItemSelected': isAreaFullySelected(area) || selectedItems.has(`area_${area.area_id}`) }">
              <div class="regionSelectorChildHeader" @click="toggleRegion(`area_${area.area_id}`)">
                <label class="regionSelectorCheckbox">
                  <input 
                    type="checkbox" 
                    :checked="isAreaFullySelected(area) || selectedItems.has(`area_${area.area_id}`)"
                    :indeterminate="hasPartialSelectionArea(area)" 
                    @click.stop
                    @change="handleAreaSelection(area, $event)" />
                  <span class="regionSelectorCheckmark"></span>
                  <span class="regionSelectorLabel">{{ area.area_name }}</span>
                </label>
                <button v-if="area.bus_stops && area.bus_stops.length > 0"
                  :class="['regionSelectorToggle', { 'regionSelectorToggleExpanded': expandedRegions.has(`area_${area.area_id}`) }]"
                  @click.stop="toggleRegion(`area_${area.area_id}`)">
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="1.5" fill="none" />
                  </svg>
                </button>
              </div>

              <!-- 巴士站层级 -->
              <div v-if="expandedRegions.has(`area_${area.area_id}`) && area.bus_stops"
                class="regionSelectorSubChildren">
                <div v-for="busStop in area.bus_stops" :key="busStop.bus_stop_id" class="regionSelectorSubChildItem"
                  :class="{ 'regionSelectorSubChildItemSelected': isNodeSelected(`bus_${busStop.bus_stop_id}`, 'bus_stop', busStop.bus_stop_id) || selectedItems.has(`bus_${busStop.bus_stop_id}`) }">
                  <label class="regionSelectorCheckbox">
                    <input 
                      type="checkbox" 
                      :checked="isNodeSelected(`bus_${busStop.bus_stop_id}`, 'bus_stop', busStop.bus_stop_id) || selectedItems.has(`bus_${busStop.bus_stop_id}`)"
                      @change="handleBusStopSelection(busStop, $event)" />
                    <span class="regionSelectorCheckmark"></span>
                    <span class="regionSelectorLabel">{{ busStop.bus_stop_name }}</span>
                  </label>
                  <span v-if="busStop.labels && busStop.labels.length > 0" class="regionSelectorTagLabel">
                    {{ getTagText(busStop.labels) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="regionSelectorFooter">
      <button class="regionSelectorConfirmBtn" :disabled="!selectedNode" @click="handleConfirm">
        {{ confirmButtonText }}
      </button>
    </div>
  </div>
</template>


<style lang="scss">

.regionSelectorLabels {
  color: #26499D;
}

.regionSelectorItems {
  border-top: 1px solid #DFDFDF;
  transition: background-color 0.2s ease;
}

.regionSelectorItems:last-child {
  margin-bottom: 20px;
}

.regionSelectorItems:last-of-type {
  border-bottom: 1px solid #DFDFDF;
}

.regionSelectorItem {
  border-top: 1px solid #DFDFDF;
  margin: 0 20px;
  transition: background-color 0.2s ease;
}

.regionSelectorItem:last-child {
  margin-bottom: 60px;
}

.regionSelectorList>.regionSelectorItem:last-of-type {
  border-bottom: 1px solid #DFDFDF;
}

.regionSelector {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;


  &Header {
    padding: 12px 16px;
  }

  &HeaderTop {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  &BackBtn {
    padding: 4px;
    border: none;
    background: transparent;
    color: #666;
    margin-right: 8px;
  }

  &Title {
    font-size: 16px;
    font-weight: 500;
    color: #26499D;
    margin: 0;
    flex: 1;
    text-align: center;
  }

  &SearchBox {
    margin-bottom: 12px;
  }

  &SearchInput {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #4096ff;
    }

    &::placeholder {
      color: #999;
    }
  }


  &Tags {
    text-align: left;
    overflow: hidden;
  }

  &Tag {
    height: 32px;
    border-radius: 6px;
    font-size: 13px;
    color: #000;
    border: 1px solid #9CBCD4;
    width: calc(25% - 10px);
    min-width: 50px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin: 5px;

    &Active {
      background: #9CBCD4;
      color: #000;
    }
  }


  &Content {
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    margin-bottom: 80px;
  }

  &List {
    padding: 0;
  }

  &ItemHeader,
  &ChildHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    transition: background-color 0.2s ease;
  }

  &Checkbox {
    display: flex;
    align-items: center;
    flex: 1;

    input[type="checkbox"] {
      display: none;
    }
  }

  &Checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #26499D;
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    transition: all 0.2s ease;

    input:checked+& {
      background: #26499D;
      border-color: #26499D;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -60%) rotate(45deg);
        width: 4px;
        height: 8px;
        border: 2px solid #ffffff;
        border-top: none;
        border-left: none;
      }
    }

    // 不确定状态样式
    input:indeterminate+& {
      background: #26499D;
      border-color: #26499D;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 2px;
        background: #ffffff;
        border: none;
      }
    }
  }

  &Label {
    font-size: 14px;
    color: #333;
    user-select: none;
  }

  &Toggle {
    padding: 4px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: #26499D;
    transition: all 0.2s ease;

    &Expanded {
      transform: rotate(90deg);
    }
  }

  &Children {
    border-top: 1px solid #f0f0f0;
  }

  &ChildItem {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #E7F2FA;
    }
  }

  &ChildHeader {
    padding: 8px 16px 8px 32px;
  }

  &SubChildren {
    border-top: 1px solid #e0e0e0;
  }

  &SubChildItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px 12px 48px;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #E7F2FA;
    }
  }

  &TagLabel {
    font-size: 12px;
    color: #000;
    border: 1px solid #9CBCD4;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
  }

  &Footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  &ConfirmBtn {
    width: 100%;
    padding: 12px 24px;
    background: #26499D;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:disabled {
      background: #DFDFDF;
      color: #7D7D7D;
      cursor: not-allowed;
    }

  }
}
</style>
