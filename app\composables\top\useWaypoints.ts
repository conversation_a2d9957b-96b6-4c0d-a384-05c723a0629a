// pages/top/composables/useWaypoints.ts
import { ref, computed, type Ref, type ComputedRef } from 'vue'

export interface Waypoint {
  id: number
  location: string
}

export interface WaypointsManager {
  waypoints: Ref<Waypoint[]>
  showWaypoints: Ref<boolean>
  addWaypoint: () => void
  removeWaypoint: (id: number) => void
  toggleWaypoint: () => void
  canAddMoreWaypoints: ComputedRef<boolean>
  waypointButtonText: ComputedRef<string>
  clearWaypoints: () => void
  getValidWaypoints: () => string[]
}

export function useWaypoints(maxWaypoints: number = 3): WaypointsManager {
  const waypoints = ref<Waypoint[]>([])
  const showWaypoints = ref<boolean>(false)

  // 添加经由地
  const addWaypoint = (): void => {
    if (waypoints.value.length < maxWaypoints) {
      waypoints.value.push({
        id: Date.now() + Math.random(),
        location: ''
      })
    }
  }

  // 删除经由地
  const removeWaypoint = (id: number): void => {
    const index = waypoints.value.findIndex(w => w.id === id)
    if (index > -1) {
      waypoints.value.splice(index, 1)
    }

    // 如果没有经由地了，隐藏经由地区域
    if (waypoints.value.length === 0) {
      showWaypoints.value = false
    }
  }

  // 切换经由地显示
  const toggleWaypoint = (): void => {
    if (!showWaypoints.value) {
      showWaypoints.value = true
      if (waypoints.value.length === 0) {
        addWaypoint()
      }
    }
  }

  // 判断是否可以添加更多经由地
  const canAddMoreWaypoints = computed((): boolean => {
    return waypoints.value.length < maxWaypoints
  })

  // 经由地按钮文本
  const waypointButtonText = computed((): string => {
    if (showWaypoints.value && waypoints.value.length > 0) {
      return `経由地を追加 (${waypoints.value.length}/${maxWaypoints})`
    }
    return '経由地を選択（任意）'
  })

  // 清理经由地数据
  const clearWaypoints = (): void => {
    waypoints.value = []
    showWaypoints.value = false
  }

  // 获取有效的经由地列表（过滤空值）
  const getValidWaypoints = (): string[] => {
    return waypoints.value
      .map(w => w.location)
      .filter(loc => loc && loc !== '経由地を選択' && loc.trim() !== '')
  }

  return {
    waypoints,
    showWaypoints,
    addWaypoint,
    removeWaypoint,
    toggleWaypoint,
    canAddMoreWaypoints,
    waypointButtonText,
    clearWaypoints,
    getValidWaypoints
  }
}