<script lang="ts" setup>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { computed } from 'vue'
import { useRoute } from '#app'

const route = useRoute()
const hideFooter = computed(() => route.meta.hideFooter)
const footerPlaceholderHeight = computed(() => route.meta.footerPlaceholderHeight)

// 切换抽屉菜单的显示/隐藏
const toggleDrawer = () => {
  console.log('Drawer toggled')
}

// ログイン
const handleLogin = () => {
  console.log('Login button clicked')
}
</script>

<template>
  <Header @toggle-drawer="toggleDrawer" @login="handleLogin"/>

  <VMain>
    <slot/>
  </VMain>

  <div v-show="!hideFooter">
    <Footer/>
  </div>
  <div v-if="!!footerPlaceholderHeight" :style="`height: ${footerPlaceholderHeight}px`">
  </div>
</template>