<template>
  <div class="login-page">
    <!-- Page Title Header -->
    <div class="page-title-header">
      <h1 class="page-title">ログイン</h1>
    </div>

    <!-- Main Content -->
    <div class="login-content">
      <div class="login-form-container">
        <!-- Login Form -->
        <v-form ref="formRef" class="login-form">
          <!-- Error Message -->
          <div v-if="hasError" ref="errorMessageRef" class="error-message">
            <div class="error-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <path d="M16 3L29.856 26H2.144L16 3Z" fill="#D00000" />
                <path
                  d="M16 12V18"
                  stroke="#FFFFFF"
                  stroke-width="2"
                  stroke-linecap="round"
                />
                <circle cx="16" cy="22" r="1" fill="#FFFFFF" />
              </svg>
            </div>
            <span class="error-text">{{ errorMessage }}</span>
          </div>

          <!-- Email Input -->
          <div class="input-group">
            <div class="input-label">
              <span class="label-text">メールアドレス</span>
              <span class="required-mark">*</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="email"
                type="email"
                :rules="validationRules.email"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
              />
            </div>
          </div>

          <!-- Password Input -->
          <div class="input-group">
            <div class="input-label">
              <span class="label-text">パスワード</span>
              <span class="required-mark">*</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                :rules="validationRules.password"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword = !showPassword"
              />
            </div>
            <!-- <div class="input-rule">半角英数字8文字で入力してください</div> -->
          </div>

          <!-- Remember Login Checkbox -->
          <div class="checkbox-container">
            <div class="checkbox-wrapper">
              <div class="checkbox-input">
                <input
                  id="remember-login"
                  v-model="rememberLogin"
                  type="checkbox"
                  class="checkbox-native"
                />
                <div class="checkbox-custom"></div>
              </div>
              <label for="remember-login" class="checkbox-label">
                ログイン状態を保存
              </label>
            </div>
          </div>

          <!-- Login Button and Forgot Password -->
          <div class="button-section">
            <button
              :disabled="!isFormValid || isLoading"
              :class="[
                'login-btn',
                { active: isFormValid && !isLoading, loading: isLoading }
              ]"
              @click="handleLogin"
            >
              <span>ログイン</span>
            </button>

            <button class="forgot-password-btn" @click="handleForgotPassword">
              パスワードを忘れた方
            </button>
          </div>
        </v-form>

        <!-- Divider -->
        <div class="divider"></div>

        <!-- Registration Section -->
        <div class="registration-section">
          <p class="registration-text">
            アカウントをお持ちでないお客様は、こちらから新規登録をお願いします。
          </p>

          <button class="register-btn" @click="handleRegister">
            新規会員登録
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LoginData } from '~/types/login'
import { useUserStore } from '~/stores/login'

// Page meta - 使用默认布局以包含Header组件
definePageMeta({
  layout: 'default'
})

// ユーザーストアを使用
const userStore = useUserStore()

// フォーム参照
const formRef = ref<any>(null)

// バリデーションルール
const validationRules = {
  email: [
    (v: string) => !!v || 'メールアドレスを入力してください',
    (v: string) => {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return pattern.test(v) || '有効なメールアドレスを入力してください'
    }
  ],
  password: [
    (v: string) => !!v || 'パスワードを入力してください',
    (v: string) => v.length >= 8 || 'パスワードは8文字以上で入力してください'
  ]
}

// フォーム検証関数
const validateForm = async () => {
  if (!formRef.value) return { valid: false }
  return await formRef.value.validate()
}

// バリデーション状態をリセット
const resetValidation = () => {
  if (formRef.value) {
    formRef.value.resetValidation()
  }
}

// Reactive data
const email = ref('')
const password = ref('')
const rememberLogin = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const hasError = ref(false)
const showPassword = ref(false)

// エラーメッセージ要素への参照
const errorMessageRef = ref<HTMLElement | null>(null)

// Computed - フォームの有効性をチェック
const isFormValid = computed(() => {
  // 基本的な入力チェック
  const hasBasicInput = email.value.length > 0 && password.value.length >= 8

  // メールアドレスの形式チェック
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  const isEmailValid = emailPattern.test(email.value)

  return hasBasicInput && isEmailValid
})

// エラー状態をクリア
const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

// エラーメッセージまでスクロール
const scrollToError = () => {
  nextTick(() => {
    if (errorMessageRef.value) {
      errorMessageRef.value.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  })
}

// 入力値変更時にエラーをクリア（全体のエラーメッセージのみ）
watch([email, password], () => {
  if (hasError.value) {
    clearError()
  }
})

// Methods
const handleLogin = async () => {
  // フォームバリデーションを実行
  const validationResult = await validateForm()
  if (!validationResult.valid) {
    hasError.value = true
    errorMessage.value =
      'メールアドレスまたはパスワードの形式が正しくありません。'
    scrollToError()
    return
  }

  try {
    isLoading.value = true
    clearError()

    const loginData: LoginData = {
      userId: email.value,
      password: password.value,
      remember: rememberLogin.value
    }

    // ストアのログインメソッドを使用してグローバル状態を管理
    const response = await userStore.login(loginData)

    if (response.code === 200) {
      // ログイン成功 - ストアが自動的にトークンとユーザー情報を保存
      console.log('ログイン成功:', response.data)
      console.log(
        'ユーザー情報がストアに保存されました:',
        userStore.userProfile
      )
      navigateTo('/dashboard') // または適切なページにリダイレクト
    } else {
      // ログイン失敗
      hasError.value = true
      errorMessage.value =
        'メールアドレスまたはパスワードが正しくありません。再確認してください。'
      scrollToError()
    }
  } catch (error) {
    console.error('ログインエラー:', error)
    hasError.value = true
    errorMessage.value =
      'メールアドレスまたはパスワードが正しくありません。再確認してください。'
    scrollToError()
  } finally {
    isLoading.value = false
  }
}

const handleForgotPassword = () => {
  // パスワード忘れ処理
  console.log('パスワードを忘れた方がクリックされました')
}

const handleRegister = () => {
  // 新規登録ページに遷移
  navigateTo('/register')
}

// goBack函数已不需要，因为Header组件会处理导航
</script>

<style scoped>
.login-page {
  width: 375px;
  min-height: calc(100vh - 50px); /* 减去Header高度 */
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.page-title-header {
  background-color: #ffffff;
  padding: 12px 16px;
  text-align: center;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

.login-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.login-form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
}

.required-mark {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #d00000;
}

.input-field {
  width: 100%;
}

.custom-input {
  width: 100%;
  height: 48px;
  border: 2px solid #dfdfdf;
  border-radius: 2px;
  background-color: #ffffff;
  padding: 13px 18px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
  outline: none;
  box-sizing: border-box;
}

.custom-input:focus {
  border-color: #26499d;
}

.custom-input.error {
  border-color: #d00000;
}

.input-rule {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #7d7d7d;
  margin-top: 4px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-input {
  position: relative;
  width: 20px;
  height: 20px;
}

.checkbox-native {
  position: absolute;
  opacity: 0;
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
}

.checkbox-custom {
  width: 15.5px;
  height: 15.5px;
  border: 1.5px solid #26499d;
  border-radius: 1.72px;
  background-color: #ffffff;
  position: absolute;
  top: 2.5px;
  left: 2.5px;
  pointer-events: none;
}

.checkbox-native:checked + .checkbox-custom {
  background-color: #26499d;
}

.checkbox-native:checked + .checkbox-custom::after {
  content: '✓';
  color: #ffffff;
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.checkbox-label {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  cursor: pointer;
}

.button-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.login-btn {
  width: 343px;
  height: 50px;
  background-color: #fbe4df;
  border: none;
  border-radius: 35px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:disabled {
  background-color: #fbe4df;
  opacity: 0.6;
  cursor: not-allowed;
}

.login-btn.active {
  background-color: #ed785f;
  color: #ffffff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-btn.active:hover {
  background-color: #d66b4a;
}

.login-btn.loading {
  background-color: #ed785f;
  color: #ffffff;
  cursor: not-allowed;
}

.forgot-password-btn {
  background: none;
  border: none;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #26499d;
  cursor: pointer;
  text-decoration: none;
  padding: 0;
}

.forgot-password-btn:hover {
  text-decoration: underline;
}

.divider {
  width: 341px;
  height: 1px;
  background-color: #dfdfdf;
  margin: 0 auto;
}

.registration-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.registration-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.57;
  color: #000000;
  margin: 0;
  text-align: left;
}

.register-btn {
  width: 343px;
  height: 50px;
  background-color: #e7f2fa;
  border: 1px solid #9cbcd4;
  border-radius: 35px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #26499d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.register-btn:hover {
  background-color: #d6e8f5;
}

/* Error Message Styles */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 8px;
  animation: fadeInError 0.3s ease-in-out;
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #d00000;
  line-height: 1.375;
}

/* Password Field Styles */
.password-field {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 13px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  opacity: 0.7;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Vuetify Text Field Custom Styles */
:deep(.custom-text-field .v-field) {
  border: 2px solid #dfdfdf;
  border-radius: 2px;
  background-color: #ffffff;
  height: 48px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
}

:deep(.custom-text-field .v-field--focused) {
  border-color: #26499d;
}

:deep(.custom-text-field .v-field--error) {
  border-color: #d00000;
}

:deep(.custom-text-field .v-field__input) {
  padding: 10px 18px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
  min-height: auto;
}

:deep(.custom-text-field .v-field__outline) {
  display: none;
}

:deep(.custom-text-field .v-messages) {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 12px;
  color: #d00000;
  margin-top: 4px;
}

:deep(.custom-text-field .v-field__append-inner) {
  padding-right: 13px;
}

:deep(.custom-text-field .v-icon) {
  color: #26499d;
  font-size: 24px;
}

/* Mobile responsive adjustments */
@media (max-width: 375px) {
  .login-page {
    width: 100%;
    max-width: 375px;
    min-height: calc(100vh - 50px);
  }

  .login-btn,
  .register-btn {
    width: 100%;
    max-width: 343px;
  }

  .error-message {
    margin: 0 -4px 8px -4px;
    padding: 16px 20px;
  }
}
</style>
