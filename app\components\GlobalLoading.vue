<template>
  <v-overlay :model-value="isLoading" class="loading" height="100%" width="100%">
    <v-progress-circular class="v-progress-circular" color="primary" indeterminate size="64"/>
  </v-overlay>
</template>

<script setup>
import { useHttp } from "~/composables/useHttp";

const { isLoading } = useHttp()
</script>

<style lang="scss">
.loading {
  .v-progress-circular {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
