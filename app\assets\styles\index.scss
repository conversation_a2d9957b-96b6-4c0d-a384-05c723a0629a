* {
  padding: 0;
  margin: 0;
  font-family: 'Noto Sans JP', sans-serif;
}

html {
  box-sizing: border-box;
}

.white {
  color: #fff;
}

.center {
  text-align: center;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.margin {
  margin: 14px 12px;
}

.createMargin {
  margin-bottom: 10px;
}

.createTitle {
  margin-bottom: 23px;
}

.createPadding {
  background-color: #fff;
  padding: 0 16px;
}
.createPad{
  padding: 0 16px;
}
.createBot{
  padding: 0 16px 16px 16px;
}
.regionHeaderTop {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
}

.regionSelectorBackBtn {
  background: none;
  border: none;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.regionSelectorTitle {
  color: #26499d;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: bold;
}