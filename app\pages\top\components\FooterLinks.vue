<template>
  <footer>
    <!-- 移动端背景 -->
    <div class="footer-bg footer-bg-mobile">
      <img src="~/assets/image/footerLinkImage.png" alt="フッター背景画像" loading="lazy" class="footer-bg-image" />
    </div>

    <!-- PC端背景 -->
    <div class="footer-bg footer-bg-desktop">
      <img src="~/assets/image/footerLinkImage.png" alt="フッター背景画像" class="footer-bg-image" />

    </div>

    <!-- 内容容器 -->
    <!-- <ClientOnly> -->
    <div>
      <div class="footer-nav">
        <ul class="footer-links">
          <li v-for="link in links" :key="link.text" class="footer-item">
            <a :href="link.type === 'external' ? link.path : '#'" class="footer-link" :aria-label="link.text"
              :target="link.type === 'external' ? '_blank' : '_self'"
              :rel="link.type === 'external' ? 'noopener noreferrer' : ''" @click="handleLinkClick(link.text, $event)">
              {{ link.text }}
            </a>
          </li>
        </ul>
      </div>
    </div>
    <!-- </ClientOnly> -->

  </footer>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

// 链接配置，包含文本、路由路径和链接类型
const links = [
  {
    text: "お問い合わせ",
    path: "/contact",
    type: "internal"
  },
  {
    text: "利用規約",
    path: "/terms",
    type: "internal"
  },
  {
    text: "プライバシーポリシー",
    path: "/privacy",
    type: "internal"
  },
  {
    text: "サイトマップ",
    path: "/sitemap",
    type: "internal"
  },
  {
    text: "会社情報",
    path: "https://example.com/company", // 外部链接示例
    type: "external"
  }
];

const handleLinkClick = (linkText, event) => {
  event.preventDefault();

  // 查找对应的链接配置
  const linkConfig = links.find(link => link.text === linkText);

  if (!linkConfig) {
    console.warn(`Link config not found for: ${linkText}`);
    return;
  }

  console.log(`Clicked: ${linkText}, Type: ${linkConfig.type}, Path: ${linkConfig.path}`);

  if (linkConfig.type === 'external') {
    handleExternalLink(linkConfig.path);
  } else {
    navigateToPage(linkConfig.path);
  }
};

const navigateToPage = (path) => {
  router.push(path);
};

const handleExternalLink = (url) => {
  window.open(url, '_blank', 'noopener,noreferrer');
};
</script>
<style scoped>
.footer-bg-image,
.footer-bg-images {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.footer-nav {
  background-color: #9cbcd4;
  width: 100%;
  margin: 0 auto;
}

.footer-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.footer-item {
  margin: 0;
  /* 为伪元素竖线预留空间，避免文字与竖线重叠 */
  position: relative;
}

/* 核心：添加白色竖线（默认隐藏，PC端显示） */
.footer-item:not(:last-child)::after {
  content: ''; /* 伪元素必须有 content */
  display: none; /* 移动端默认隐藏 */
  width: 1px; /* 竖线宽度 */
  height: 16px; /* 竖线高度（根据文字大小调整，避免过高/过矮） */
  background-color: #ffffff; /* 竖线颜色：白色 */
  vertical-align: middle; /* 与文字垂直居中 */
  margin: 0 16px; /* 竖线与文字的左右间距，可按需调整 */
}

.footer-link {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: inline-block;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:visited {
    color: #ffffff;
  }
}

/* ===== 超小屏幕适配 (≤320px) ===== */
@media (max-width: 320px) {
  .footer-section {
    min-height: 160px;
  }

  .footer-container {
    padding: 50px 12px 25px;
  }

  .footer-links {
    gap: 10px;
  }

  .footer-link {
    font-size: 12px;
    padding: 6px 10px;
  }
}

/* ===== 小屏幕适配 (321px-567px) - 移动端 ===== */
@media (min-width: 321px) and (max-width: 567px) {
  .footer-bg-desktop {
    display: none;
  }

  .footer-section {
    min-height: 180px;
  }

  .footer-link {
    font-size: 13px;
    padding: 8px 12px;
  }

  .footer-bg-mobile .footer-bg-image {
    object-position: center bottom;
    height: 120px;
  }
}

/* ===== PC端适配 (≥768px) - 显示白色竖线 ===== */
@media (min-width: 768px) {
  .footer-bg-mobile {
    display: none;
  }

  .footer-bg-desktop {
    display: block;
  }

  .footer-container {
    padding: 70px 40px 45px;
  }

  .footer-links {
    flex-direction: row; /* 横向排列导航项 */
    justify-content: center;
    gap: 0;
    flex-wrap: wrap;
  }

  .footer-link {
    font-size: 16px;
    padding: 12px 0;
  }

  .footer-bg-desktop .footer-bg-image {
    object-position: center center;
  }

  /* PC端：显示竖线 */
  .footer-item:not(:last-child)::after {
    display: inline-block;
  }
}

/* 1024px及以上样式 */
@media (min-width: 1024px) and (max-width: 1919px) {
  .footer-bg-desktop {
    z-index: 9;
  }

  .footer-container {
    padding: 0;
    height: 232px;
  }

  .footer-bg-images {
    background-color: #9cbcd4;
  }

  .footer-bg-mobile {
    display: none;
  }

  .footer-links {
    width: 100%;
    background-color: #9cbcd4;
  }

  .footer-link {
    font-size: 17px;
  }

  /* 调整大屏竖线间距（可选，按需优化） */
  .footer-item:not(:last-child)::after {
    margin: 0 20px; /* 大屏可适当加宽竖线间距，更美观 */
    height: 18px; /* 匹配17px文字高度，调整竖线高度 */
  }
}

/* ===== 大屏幕优化 (≥1920px) ===== */
@media (min-width: 1920px) {
  .footer-bg-desktop {
    z-index: 9;
  }

  .footer-container {
    padding: 0;
    height: 232px;
  }

  .footer-bg-images {
    background-color: #9cbcd4;
  }

  .footer-bg-mobile {
    display: none;
  }

  .footer-links {
    width: 100%;
    background-color: #9cbcd4;
  }

  .footer-link {
    font-size: 17px;
  }

  /* 大屏竖线样式（与1024px保持一致，确保统一） */
  .footer-item:not(:last-child)::after {
    display: inline-block;
    height: 18px;
    margin: 0 20px;
  }
}
</style>
