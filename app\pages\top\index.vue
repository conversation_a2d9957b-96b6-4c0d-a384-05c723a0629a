<script setup lang="ts">
import { ref } from 'vue'
import Introduction from '~/pages/top/components/Introduction.vue'
import TravelSearchForm from '~/pages/top/components/TravelSearchForm.vue'
import headerImageUrl from '~/assets/image/rightIcon.png'
import mapSearchImageUrl from '~/assets/image/mapSearch.png'
import Product from '~/pages/top/components/Product.vue'
import EventSpot from '~/pages/top/components/EventSpot.vue'
import NoticeList from '~/pages/top/components/NoticeList.vue'
import FooterLinks from '~/pages/top/components/FooterLinks.vue'
import RideReservationCard from '~/pages/top/components/RideReservationCard.vue'
import { isLogin } from '~/utils/auth'
import { useRouter } from 'vue-router'
import MapSearch from '~/pages/top/components/MapSearch.vue'

const router = useRouter()
const isLoggedIn = computed(() => isLogin())
const headerImage = headerImageUrl
const mapSearchImage = mapSearchImageUrl
const currentTab = ref('片道')

const handleTabClick = (tabValue) => {
  currentTab.value = tabValue
}
const galleryData = [
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1481277542470-605612bd2d61?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1481277542470-605612bd2d61?w=800&h=400&fit=crop'
  }
]
const tickets = ref([
  {
    id: 'T20230501001',
    arr: [
      {
        id: 101,
        start: '京都',
        end: '奈良',
        next: '大阪',
        final: '神戸',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '1便目',
        startDate: '5/1 (月)',
        endDate: '5/1 (月)',
        startTime: '08:30',
        endTime: '12:45',
        startLocation: '京都駅中央口',
        endLocation: '神戸三宮駅'
      },
      {
        id: 102,
        start: '京都',
        end: '大阪',
        next: '姫路',
        final: '岡山',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '2便目',
        startDate: '5/2 (火)',
        endDate: '5/2 (火)',
        startTime: '14:15',
        endTime: '18:30',
        startLocation: '京都駅新幹線口',
        endLocation: '岡山駅東口'
      },
      {
        id: 103,
        start: '京都',
        end: '大阪',
        next: '姫路',
        final: '岡山',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '3便目',
        startDate: '5/2 (日)',
        endDate: '5/2 (日)',
        startTime: '14:15',
        endTime: '18:30',
        startLocation: '京都駅新幹線口',
        endLocation: '岡山駅東口'
      }
    ]
  },
  {
    id: 'T20230503002',
    arr: [
      {
        id: 201,
        start: '札幌',
        end: '旭川',
        next: '富良野',
        final: '帯広',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '3便目',
        startDate: '5/3 (水)',
        endDate: '5/4 (木)',
        startTime: '09:10',
        endTime: '16:40',
        startLocation: '札幌駅北口',
        endLocation: '帯広駅'
      },
      {
        id: 202,
        start: '函館',
        end: '小樽',
        next: '札幌',
        final: '岩見沢',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '4便目',
        startDate: '5/5 (金)',
        endDate: '5/5 (金)',
        startTime: '11:20',
        endTime: '19:50',
        startLocation: '函館駅中央口',
        endLocation: '岩見沢駅'
      }
    ]
  },
  {
    id: 'T20230507003',
    arr: [
      {
        id: 301,
        start: '東京',
        end: '横浜',
        next: '静岡',
        final: '名古屋',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '5便目',
        startDate: '5/7 (日)',
        endDate: '5/7 (日)',
        startTime: '07:00',
        endTime: '12:30',
        startLocation: '東京駅八重洲口',
        endLocation: '名古屋駅東口'
      }
    ]
  }
])

// 检索事件
const handleSearch = (searchData) => {
  console.log('Search data:', searchData)
}

const reservation = () => {
  router.push(`/confirm/reservation/`)
}
</script>
<template>
  <div>
    <div class="top_Background">
      <!-- swiper轮播图 -->
      <div class="introd">
        <Introduction :gallery="galleryData" />
      </div>
      <!-- 経由地選択 -->
      <div>
        <TravelSearchForm
          :defaultTab="'片道'"
          :route="'Myルート作成'"
          :forth="'往復'"
          @search="handleSearch"
          @tabClick="handleTabClick"
        />
      </div>
      <div>
        <div class="center">
          <MapSearch />
        </div>
        <div class="center" v-if="isLoggedIn">
          <div class="header-img flex">
            <div class="confirm flex" @click="reservation">
              <span>予約の確認・変更・キャンセル</span>
              <img
                :src="headerImage"
                alt="画像の読み込みに失敗しました"
                class="head-img"
              />
            </div>
          </div>
        </div>
        <div class="centerRide" v-else>
          <RideReservationCard :tickets="tickets" />
        </div>
      </div>
    </div>

    <!-- お得な商品 -->
    <div class="product">
      <Product />
    </div>
    <!-- 观光地 -->
    <div class="spots">
      <EventSpot
        sectionTitle="観光地・施設"
        :apiEndpoint="'spots'"
        :maxItems="6"
      />
    </div>
    <!-- 期間限定・イベント -->
    <div class="event">
      <EventSpot
        sectionTitle="期間限定・イベント"
        :apiEndpoint="'event'"
        :maxItems="6"
      />
    </div>
    <!-- お知らせ -->
    <div>
      <NoticeList />
      <FooterLinks />
    </div>
  </div>
</template>

<style scoped lang="scss">
.top_Background {
  background-color: #fff7ec;

  .introd {
    padding: 8px 0 18px 0;
  }

  .map-img {
    max-width: 100%;
    margin: 20px 0;
  }

  .header-img {
    padding: 20px 17px 20px 17px;

    .confirm {
      background-color: #26499d;
      color: #fff;
      font-size: 14px;
      padding: 15px 0;
      border-radius: 40px;
      width: 341px;

      .head-img {
        width: 22px;
        height: 22px;
      }
    }
  }
}

.product {
  background-color: #e7f2fa;
}

.spots {
  background-color: #fffdea;
}

.event {
  background-color: #e4f5eb;
}

.round {
  padding-bottom: 20px;
}

.center {
  display: flex;
  justify-content: center;
}
.centerRide {
  padding: 20px 0;
}
</style>
